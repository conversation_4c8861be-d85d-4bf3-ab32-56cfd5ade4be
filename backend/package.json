{"name": "padel-chase-backend", "version": "1.0.0", "description": "Backend API for Padel Chase booking application", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "seed": "node src/scripts/seedDatabase.js", "fix-passwords": "node src/scripts/fixPasswords.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["padel", "booking", "api", "nodejs", "mongodb"], "author": "<PERSON>", "license": "ISC", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "morgan": "^1.10.1", "multer": "^2.0.2", "node-cron": "^4.2.1"}, "devDependencies": {"nodemon": "^3.1.10"}}