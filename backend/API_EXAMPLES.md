# API Usage Examples

This document provides examples of how to use the Padel Chase API endpoints.

## Base URL
```
http://localhost:5000/api/v1
```

## Authentication

### Register a new user
```bash
curl -X POST http://localhost:5000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "password": "Password123!",
    "phone": "****** 567 8999"
  }'
```

### Login
```bash
curl -X POST http://localhost:5000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!"
  }'
```

Response will include `accessToken` that you need for authenticated requests.

### Get user profile
```bash
curl -X GET http://localhost:5000/api/v1/auth/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Courts

### Get all courts
```bash
curl -X GET http://localhost:5000/api/v1/courts
```

### Get courts with filters
```bash
curl -X GET "http://localhost:5000/api/v1/courts?priceMin=30&priceMax=50&amenities=WiFi&amenities=Parking"
```

### Get court by ID
```bash
curl -X GET http://localhost:5000/api/v1/courts/COURT_ID
```

### Get court availability
```bash
curl -X GET "http://localhost:5000/api/v1/courts/COURT_ID/availability?date=2024-08-05"
```

### Create a new court (Admin only)
```bash
curl -X POST http://localhost:5000/api/v1/courts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "name": "Court 5 - Outdoor",
    "description": "Outdoor padel court with natural lighting",
    "location": "Outdoor Area",
    "capacity": 4,
    "pricePerHour": 30,
    "amenities": ["Parking"],
    "specifications": {
      "surface": "Artificial Grass",
      "lighting": "Natural",
      "size": "20m x 10m",
      "walls": "Tempered Glass",
      "ceiling": "Open",
      "flooring": "Standard surface"
    },
    "rules": ["Maximum 4 players per booking"],
    "maintenanceSchedule": "Weekly"
  }'
```

## Bookings

### Create a booking
```bash
curl -X POST http://localhost:5000/api/v1/bookings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "courtId": "COURT_ID",
    "date": "2024-08-05",
    "timeSlot": {
      "startTime": "10:00",
      "endTime": "11:00"
    },
    "players": 4,
    "specialRequests": "Please prepare equipment for beginners"
  }'
```

### Get user's bookings
```bash
curl -X GET http://localhost:5000/api/v1/bookings/my-bookings \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Get upcoming bookings
```bash
curl -X GET "http://localhost:5000/api/v1/bookings/my-bookings?upcoming=true" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Cancel a booking
```bash
curl -X PATCH http://localhost:5000/api/v1/bookings/BOOKING_ID/cancel \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "reason": "Schedule conflict"
  }'
```

### Get all bookings (Admin only)
```bash
curl -X GET http://localhost:5000/api/v1/bookings \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## Operating Hours

### Get all operating hours
```bash
curl -X GET http://localhost:5000/api/v1/operating-hours
```

### Get hours for a specific day
```bash
curl -X GET http://localhost:5000/api/v1/operating-hours/monday
```

### Get available time slots for a day
```bash
curl -X GET http://localhost:5000/api/v1/operating-hours/monday/slots
```

### Update operating hours (Admin only)
```bash
curl -X PUT http://localhost:5000/api/v1/operating-hours/monday \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "isOpen": true,
    "openTime": "08:00",
    "closeTime": "22:00",
    "specialNotes": "Extended hours on Mondays"
  }'
```

## Holidays

### Get all holidays
```bash
curl -X GET http://localhost:5000/api/v1/holidays
```

### Check if a date is a holiday
```bash
curl -X GET http://localhost:5000/api/v1/holidays/check/2024-12-25
```

### Get holidays in a date range
```bash
curl -X GET "http://localhost:5000/api/v1/holidays/range?startDate=2024-01-01&endDate=2024-12-31"
```

### Create a holiday (Admin only)
```bash
curl -X POST http://localhost:5000/api/v1/holidays \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "name": "Labor Day",
    "date": "2024-09-02",
    "isFullDay": true,
    "description": "Facility closed for Labor Day"
  }'
```

## Error Responses

All endpoints return errors in this format:
```json
{
  "success": false,
  "message": "Error description",
  "errors": [] // Optional validation errors
}
```

## Success Responses

All successful responses follow this format:
```json
{
  "success": true,
  "message": "Optional success message",
  "data": {
    // Response data
  }
}
```

## Authentication Headers

For protected endpoints, include the JWT token in the Authorization header:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## Rate Limiting

- General endpoints: 100 requests per 15 minutes
- Authentication endpoints: 5 requests per 15 minutes
- Booking endpoints: 10 requests per minute

Rate limit headers are included in responses:
- `RateLimit-Limit`: Request limit
- `RateLimit-Remaining`: Remaining requests
- `RateLimit-Reset`: Reset time
