import { body, param, query } from 'express-validator';

// User validation rules
export const validateRegister = [
  body('firstName')
    .trim()
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  
  body('lastName')
    .trim()
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  
  body('email')
    .trim()
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('phone')
    .trim()
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^\+92\d{10}$/)
    .withMessage('Please provide a valid Pakistani phone number (+92 followed by 10 digits)')
];

export const validateLogin = [
  body('email')
    .trim()
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail(),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

export const validateUpdateProfile = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  
  body('phone')
    .optional()
    .trim()
    .matches(/^\+92\d{10}$/)
    .withMessage('Please provide a valid Pakistani phone number (+92 followed by 10 digits)'),
  
  body('preferences.notifications')
    .optional()
    .isBoolean()
    .withMessage('Notifications preference must be a boolean'),
  
  body('preferences.newsletter')
    .optional()
    .isBoolean()
    .withMessage('Newsletter preference must be a boolean'),
  
  body('preferences.reminders')
    .optional()
    .isBoolean()
    .withMessage('Reminders preference must be a boolean')
];

// Court validation rules
export const validateCreateCourt = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Court name is required')
    .isLength({ max: 100 })
    .withMessage('Court name cannot exceed 100 characters'),

  body('description')
    .trim()
    .notEmpty()
    .withMessage('Court description is required')
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),

  body('capacity')
    .isInt({ min: 2, max: 6 })
    .withMessage('Capacity must be between 2 and 6'),

  body('pricePerHour')
    .isFloat({ min: 0 })
    .withMessage('Price per hour must be a positive number')
];

export const validateUpdateCourt = [
  param('id')
    .isMongoId()
    .withMessage('Invalid court ID'),
  
  body('name')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Court name cannot exceed 100 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
  
  body('capacity')
    .optional()
    .isInt({ min: 2, max: 6 })
    .withMessage('Capacity must be between 2 and 6'),
  
  body('pricePerHour')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price per hour must be a positive number')
];

// Booking validation rules
export const validateCreateBooking = [
  body('courtId')
    .isMongoId()
    .withMessage('Invalid court ID'),

  body('date')
    .isISO8601()
    .withMessage('Invalid date format')
    .custom((value) => {
      const bookingDate = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (bookingDate < today) {
        throw new Error('Booking date cannot be in the past');
      }

      const maxAdvanceDate = new Date();
      maxAdvanceDate.setDate(maxAdvanceDate.getDate() + 30);

      if (bookingDate > maxAdvanceDate) {
        throw new Error('Booking date cannot be more than 30 days in advance');
      }

      return true;
    }),

  body('timeSlot.startTime')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Invalid start time format (HH:MM)'),

  body('timeSlot.endTime')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Invalid end time format (HH:MM)')
    .custom((value, { req }) => {
      const startTime = req.body.timeSlot?.startTime;
      if (startTime && value <= startTime) {
        throw new Error('End time must be after start time');
      }
      return true;
    }),

  body('durationMinutes')
    .optional()
    .isInt({ min: 30, max: 480 })
    .withMessage('Duration must be between 30 minutes and 8 hours')
];

// Query validation
export const validateCourtQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('priceMin')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),

  query('priceMax')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number')
];

export const validateMongoId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid ID format')
];
