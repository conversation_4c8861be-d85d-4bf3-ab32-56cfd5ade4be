import { OperatingHours } from '../models/index.js';
import { validationResult } from 'express-validator';

// Get all operating hours
export const getOperatingHours = async (req, res) => {
  try {
    const operatingHours = await OperatingHours.find().sort({ dayOfWeek: 1 });

    // Create a complete week schedule
    const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const schedule = {};

    // Initialize with default closed status
    daysOfWeek.forEach(day => {
      schedule[day] = {
        dayOfWeek: day,
        isOpen: false,
        openTime: null,
        closeTime: null,
        breaks: [],
        specialNotes: null
      };
    });

    // Override with actual data
    operatingHours.forEach(hours => {
      schedule[hours.dayOfWeek] = hours;
    });

    res.json({
      success: true,
      data: { schedule }
    });

  } catch (error) {
    console.error('Get operating hours error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch operating hours',
      error: error.message
    });
  }
};

// Get operating hours for a specific day
export const getOperatingHoursByDay = async (req, res) => {
  try {
    const { day } = req.params;
    
    const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    if (!validDays.includes(day.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid day of week'
      });
    }

    const operatingHours = await OperatingHours.findOne({ dayOfWeek: day.toLowerCase() });

    if (!operatingHours) {
      return res.json({
        success: true,
        data: {
          dayOfWeek: day.toLowerCase(),
          isOpen: false,
          openTime: null,
          closeTime: null,
          breaks: [],
          specialNotes: null
        }
      });
    }

    res.json({
      success: true,
      data: operatingHours
    });

  } catch (error) {
    console.error('Get operating hours by day error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch operating hours',
      error: error.message
    });
  }
};

// Update operating hours for a specific day (admin only)
export const updateOperatingHours = async (req, res) => {
  try {
    const { day } = req.params;
    const { isOpen, openTime, closeTime, breaks, specialNotes } = req.body;

    const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    if (!validDays.includes(day.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid day of week'
      });
    }

    // Validation
    if (isOpen && (!openTime || !closeTime)) {
      return res.status(400).json({
        success: false,
        message: 'Open time and close time are required when facility is open'
      });
    }

    if (isOpen && openTime >= closeTime) {
      return res.status(400).json({
        success: false,
        message: 'Close time must be after open time'
      });
    }

    const updateData = {
      dayOfWeek: day.toLowerCase(),
      isOpen,
      openTime: isOpen ? openTime : null,
      closeTime: isOpen ? closeTime : null,
      breaks: breaks || [],
      specialNotes
    };

    const operatingHours = await OperatingHours.findOneAndUpdate(
      { dayOfWeek: day.toLowerCase() },
      updateData,
      { new: true, upsert: true, runValidators: true }
    );

    res.json({
      success: true,
      message: 'Operating hours updated successfully',
      data: operatingHours
    });

  } catch (error) {
    console.error('Update operating hours error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update operating hours',
      error: error.message
    });
  }
};

// Update multiple days at once (admin only)
export const updateMultipleOperatingHours = async (req, res) => {
  try {
    const { schedule } = req.body;

    if (!schedule || typeof schedule !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Schedule object is required'
      });
    }

    const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const updates = [];

    for (const [day, hours] of Object.entries(schedule)) {
      if (!validDays.includes(day)) {
        return res.status(400).json({
          success: false,
          message: `Invalid day: ${day}`
        });
      }

      if (hours.isOpen && (!hours.openTime || !hours.closeTime)) {
        return res.status(400).json({
          success: false,
          message: `Open time and close time are required for ${day} when facility is open`
        });
      }

      if (hours.isOpen && hours.openTime >= hours.closeTime) {
        return res.status(400).json({
          success: false,
          message: `Close time must be after open time for ${day}`
        });
      }

      const updateData = {
        dayOfWeek: day,
        isOpen: hours.isOpen,
        openTime: hours.isOpen ? hours.openTime : null,
        closeTime: hours.isOpen ? hours.closeTime : null,
        breaks: hours.breaks || [],
        specialNotes: hours.specialNotes
      };

      updates.push(
        OperatingHours.findOneAndUpdate(
          { dayOfWeek: day },
          updateData,
          { new: true, upsert: true, runValidators: true }
        )
      );
    }

    const results = await Promise.all(updates);

    res.json({
      success: true,
      message: 'Operating hours updated successfully',
      data: { schedule: results }
    });

  } catch (error) {
    console.error('Update multiple operating hours error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update operating hours',
      error: error.message
    });
  }
};

// Get available time slots for a specific day
export const getAvailableSlots = async (req, res) => {
  try {
    const { day } = req.params;
    const { duration = 60 } = req.query;

    const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    if (!validDays.includes(day.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid day of week'
      });
    }

    const operatingHours = await OperatingHours.findOne({ dayOfWeek: day.toLowerCase() });

    if (!operatingHours || !operatingHours.isOpen) {
      return res.json({
        success: true,
        data: {
          day: day.toLowerCase(),
          isOpen: false,
          slots: []
        }
      });
    }

    const slots = operatingHours.getAvailableSlots(parseInt(duration));

    res.json({
      success: true,
      data: {
        day: day.toLowerCase(),
        isOpen: true,
        operatingHours: {
          openTime: operatingHours.openTime,
          closeTime: operatingHours.closeTime
        },
        slots
      }
    });

  } catch (error) {
    console.error('Get available slots error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get available slots',
      error: error.message
    });
  }
};
