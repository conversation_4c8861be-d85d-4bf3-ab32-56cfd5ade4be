import { User, Court, Booking } from '../models/index.js';
import { startOfDay, endOfDay, subDays, format } from 'date-fns';

// Get dashboard statistics
export const getDashboardStats = async (req, res) => {
  try {
    const today = new Date();
    const startOfToday = startOfDay(today);
    const endOfToday = endOfDay(today);
    const last30Days = subDays(today, 30);

    // Get total bookings
    const totalBookings = await Booking.countDocuments();

    // Get today's bookings
    const todayBookings = await Booking.countDocuments({
      date: {
        $gte: startOfToday,
        $lte: endOfToday
      }
    });

    // Get active bookings (pending and confirmed)
    const activeBookings = await Booking.countDocuments({
      status: { $in: ['pending', 'confirmed'] }
    });

    // Get total revenue (last 30 days)
    const revenueResult = await Booking.aggregate([
      {
        $match: {
          createdAt: { $gte: last30Days },
          status: { $in: ['confirmed', 'completed'] }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$totalPrice' }
        }
      }
    ]);

    const totalRevenue = revenueResult[0]?.totalRevenue || 0;

    // Get total courts
    const totalCourts = await Court.countDocuments({ status: 'active' });

    // Get total users
    const totalUsers = await User.countDocuments({ role: 'customer' });

    // Calculate court utilization (simplified)
    const courtUtilization = totalCourts > 0 ? Math.round((activeBookings / (totalCourts * 10)) * 100) : 0;

    // Get booking trends (last 7 days)
    const bookingTrends = await Booking.aggregate([
      {
        $match: {
          createdAt: { $gte: subDays(today, 7) }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          count: { $sum: 1 },
          revenue: { $sum: '$totalPrice' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    res.json({
      success: true,
      data: {
        totalBookings,
        todayBookings,
        activeBookings,
        totalRevenue,
        totalCourts,
        totalUsers,
        courtUtilization,
        bookingTrends
      }
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics'
    });
  }
};

// Get recent bookings
export const getRecentBookings = async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const bookings = await Booking.find()
      .populate('court', 'name')
      .populate('customer', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: { bookings }
    });

  } catch (error) {
    console.error('Get recent bookings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent bookings'
    });
  }
};

// Get all bookings with filters
export const getAllBookings = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      courtId,
      startDate,
      endDate,
      search
    } = req.query;

    const filter = {};

    // Status filter
    if (status) {
      filter.status = status;
    }

    // Court filter
    if (courtId) {
      filter.court = courtId;
    }

    // Date range filter
    if (startDate || endDate) {
      filter.date = {};
      if (startDate) filter.date.$gte = new Date(startDate);
      if (endDate) filter.date.$lte = new Date(endDate);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    let query = Booking.find(filter)
      .populate('court', 'name location')
      .populate('customer', 'firstName lastName email phone')
      .sort({ date: -1, 'timeSlot.startTime': 1 });

    // Search filter (applied after population)
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query = query.where({
        $or: [
          { 'customer.firstName': searchRegex },
          { 'customer.lastName': searchRegex },
          { 'customer.email': searchRegex },
          { 'court.name': searchRegex }
        ]
      });
    }

    const bookings = await query.skip(skip).limit(parseInt(limit));
    const total = await Booking.countDocuments(filter);

    res.json({
      success: true,
      data: {
        bookings,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get all bookings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookings'
    });
  }
};

// Update booking status
export const updateBookingStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['pending', 'confirmed', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    const booking = await Booking.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    ).populate('court', 'name').populate('customer', 'firstName lastName email');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    res.json({
      success: true,
      data: { booking }
    });

  } catch (error) {
    console.error('Update booking status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update booking status'
    });
  }
};

// Delete booking
export const deleteBooking = async (req, res) => {
  try {
    const { id } = req.params;

    const booking = await Booking.findByIdAndDelete(id);

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    res.json({
      success: true,
      message: 'Booking deleted successfully'
    });

  } catch (error) {
    console.error('Delete booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete booking'
    });
  }
};

// Get all courts
export const getAllCourts = async (req, res) => {
  try {
    const courts = await Court.find().sort({ name: 1 });

    res.json({
      success: true,
      data: { courts }
    });

  } catch (error) {
    console.error('Get all courts error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch courts'
    });
  }
};

// Create new court
export const createCourt = async (req, res) => {
  try {
    const courtData = req.body;

    const court = new Court(courtData);
    await court.save();

    res.status(201).json({
      success: true,
      data: { court }
    });

  } catch (error) {
    console.error('Create court error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create court'
    });
  }
};

// Update court
export const updateCourt = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const court = await Court.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    res.json({
      success: true,
      data: { court }
    });

  } catch (error) {
    console.error('Update court error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update court'
    });
  }
};

// Delete court
export const deleteCourt = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if court has active bookings
    const activeBookings = await Booking.countDocuments({
      court: id,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (activeBookings > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete court with active bookings'
      });
    }

    const court = await Court.findByIdAndDelete(id);

    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    res.json({
      success: true,
      message: 'Court deleted successfully'
    });

  } catch (error) {
    console.error('Delete court error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete court'
    });
  }
};

// Upload court image
export const uploadCourtImage = async (req, res) => {
  try {
    const { id } = req.params;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No image file provided'
      });
    }

    // In a real application, you would upload to cloud storage (AWS S3, Cloudinary, etc.)
    // For now, we'll just store the file path
    const imageUrl = `/uploads/courts/${req.file.filename}`;

    const court = await Court.findByIdAndUpdate(
      id,
      { image: imageUrl },
      { new: true }
    );

    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    res.json({
      success: true,
      data: { imageUrl }
    });

  } catch (error) {
    console.error('Upload court image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload image'
    });
  }
};

// Get all users
export const getAllUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      role,
      status,
      search
    } = req.query;

    const filter = {};

    // Role filter
    if (role) {
      filter.role = role;
    }

    // Status filter
    if (status) {
      filter.status = status;
    }

    // Search filter
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      filter.$or = [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { email: searchRegex }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const users = await User.find(filter)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(filter);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
};

// Update user status
export const updateUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['active', 'inactive', 'suspended'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    const user = await User.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { user }
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user status'
    });
  }
};

// Get booking analytics
export const getBookingAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const filter = {};
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    // Booking status distribution
    const statusDistribution = await Booking.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Bookings by court
    const courtBookings = await Booking.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'courts',
          localField: 'court',
          foreignField: '_id',
          as: 'courtInfo'
        }
      },
      {
        $group: {
          _id: '$court',
          courtName: { $first: '$courtInfo.name' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Daily booking trends
    const dailyTrends = await Booking.aggregate([
      { $match: filter },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      data: {
        statusDistribution,
        courtBookings,
        dailyTrends
      }
    });

  } catch (error) {
    console.error('Get booking analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch booking analytics'
    });
  }
};

// Get revenue analytics
export const getRevenueAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const filter = {
      status: { $in: ['confirmed', 'completed'] }
    };

    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    // Total revenue
    const totalRevenue = await Booking.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          total: { $sum: '$totalPrice' }
        }
      }
    ]);

    // Daily revenue
    const dailyRevenue = await Booking.aggregate([
      { $match: filter },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          revenue: { $sum: '$totalPrice' },
          bookings: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Revenue by court
    const courtRevenue = await Booking.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'courts',
          localField: 'court',
          foreignField: '_id',
          as: 'courtInfo'
        }
      },
      {
        $group: {
          _id: '$court',
          courtName: { $first: '$courtInfo.name' },
          revenue: { $sum: '$totalPrice' },
          bookings: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        totalRevenue: totalRevenue[0]?.total || 0,
        dailyRevenue,
        courtRevenue
      }
    });

  } catch (error) {
    console.error('Get revenue analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch revenue analytics'
    });
  }
};

// Get settings
export const getSettings = async (req, res) => {
  try {
    // For now, return default settings
    // In a real app, you'd have a Settings model
    const settings = {
      facilityName: 'Padel Chase',
      contactEmail: '<EMAIL>',
      contactPhone: '+92-300-1234567',
      address: 'Islamabad, Pakistan',
      currency: 'PKR',
      timezone: 'Asia/Karachi',
      bookingAdvanceDays: 30,
      cancellationHours: 2,
      defaultCourtPrice: 1000,
      emailNotifications: true,
      smsNotifications: false
    };

    res.json({
      success: true,
      data: { settings }
    });

  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings'
    });
  }
};

// Update settings
export const updateSettings = async (req, res) => {
  try {
    const settings = req.body;

    // In a real app, you'd save to a Settings model
    // For now, just return the updated settings
    res.json({
      success: true,
      data: { settings }
    });

  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings'
    });
  }
};
