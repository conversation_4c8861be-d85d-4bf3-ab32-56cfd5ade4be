import { Court, Booking, OperatingHours } from '../models/index.js';
import { calculatePrice } from '../utils/pricing.js';
import { validationResult } from 'express-validator';

// Get all courts with filtering and pagination
export const getCourts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      priceMin,
      priceMax,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    // Build filter object
    const filter = { status: 'active' };

    // Text search
    if (search) {
      filter.$text = { $search: search };
    }

    // Price range filter
    if (priceMin || priceMax) {
      filter.pricePerHour = {};
      if (priceMin) filter.pricePerHour.$gte = parseFloat(priceMin);
      if (priceMax) filter.pricePerHour.$lte = parseFloat(priceMax);
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Execute query
    const courts = await Court.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Court.countDocuments(filter);

    res.json({
      success: true,
      data: {
        courts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get courts error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch courts',
      error: error.message
    });
  }
};

// Get single court by ID
export const getCourtById = async (req, res) => {
  try {
    const { id } = req.params;

    const court = await Court.findOne({ _id: id, status: 'active' });

    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    res.json({
      success: true,
      data: { court }
    });

  } catch (error) {
    console.error('Get court error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch court',
      error: error.message
    });
  }
};

// Create new court (admin only)
export const createCourt = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const courtData = req.body;

    // Set next maintenance date if not provided
    if (!courtData.nextMaintenance) {
      const nextMaintenance = new Date();
      switch (courtData.maintenanceSchedule) {
        case 'Daily':
          nextMaintenance.setDate(nextMaintenance.getDate() + 1);
          break;
        case 'Weekly':
          nextMaintenance.setDate(nextMaintenance.getDate() + 7);
          break;
        case 'Bi-weekly':
          nextMaintenance.setDate(nextMaintenance.getDate() + 14);
          break;
        case 'Monthly':
          nextMaintenance.setMonth(nextMaintenance.getMonth() + 1);
          break;
        default:
          nextMaintenance.setDate(nextMaintenance.getDate() + 7);
      }
      courtData.nextMaintenance = nextMaintenance;
    }

    const court = new Court(courtData);
    await court.save();

    res.status(201).json({
      success: true,
      message: 'Court created successfully',
      data: { court }
    });

  } catch (error) {
    console.error('Create court error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create court',
      error: error.message
    });
  }
};

// Update court (admin only)
export const updateCourt = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updateData = req.body;

    const court = await Court.findOneAndUpdate(
      { _id: id, status: 'active' },
      updateData,
      { new: true, runValidators: true }
    );

    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    res.json({
      success: true,
      message: 'Court updated successfully',
      data: { court }
    });

  } catch (error) {
    console.error('Update court error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update court',
      error: error.message
    });
  }
};

// Delete court (admin only) - soft delete
export const deleteCourt = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if court has future bookings
    const futureBookings = await Booking.countDocuments({
      court: id,
      date: { $gte: new Date() },
      status: { $in: ['pending', 'confirmed'] }
    });

    if (futureBookings > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete court with future bookings'
      });
    }

    const court = await Court.findOneAndUpdate(
      { _id: id, status: 'active' },
      { status: 'inactive' },
      { new: true }
    );

    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    res.json({
      success: true,
      message: 'Court deleted successfully'
    });

  } catch (error) {
    console.error('Delete court error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete court',
      error: error.message
    });
  }
};

// Get court availability for a specific date
export const getCourtAvailability = async (req, res) => {
  try {
    const { id } = req.params;
    const { date, duration = 60 } = req.query;

    if (!date) {
      return res.status(400).json({
        success: false,
        message: 'Date is required'
      });
    }

    const court = await Court.findOne({ _id: id, status: 'active' });
    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    // Check if court is available for booking
    if (court.status !== 'active') {
      return res.json({
        success: true,
        data: {
          available: false,
          reason: 'Court is not available for booking',
          timeSlots: []
        }
      });
    }

    // Get day of week for operating hours
    const bookingDate = new Date(date);
    const dayOfWeek = bookingDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

    // Get operating hours for the day
    const operatingHours = await OperatingHours.findOne({ dayOfWeek });
    if (!operatingHours || !operatingHours.isOpen) {
      return res.json({
        success: true,
        data: {
          available: false,
          reason: 'Facility is closed on this day',
          timeSlots: []
        }
      });
    }

    let dayHours = operatingHours;

    // Get existing bookings for this court and date
    const existingBookings = await Booking.find({
      court: id,
      date: {
        $gte: new Date(bookingDate.getFullYear(), bookingDate.getMonth(), bookingDate.getDate()),
        $lt: new Date(bookingDate.getFullYear(), bookingDate.getMonth(), bookingDate.getDate() + 1)
      },
      status: { $in: ['pending', 'confirmed'] }
    });

    console.log(`Checking availability for court ${id} on ${date}`);
    console.log(`Found ${existingBookings.length} existing bookings:`, existingBookings.map(b => ({
      timeSlot: b.timeSlot,
      status: b.status
    })));

    // Generate available time slots
    const timeSlots = generateTimeSlots(dayHours, existingBookings, court.pricePerHour, parseInt(duration), bookingDate);

    res.json({
      success: true,
      data: {
        available: timeSlots.length > 0,
        timeSlots,
        operatingHours: {
          openTime: dayHours.openTime,
          closeTime: dayHours.closeTime
        }
      }
    });

  } catch (error) {
    console.error('Get court availability error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get court availability',
      error: error.message
    });
  }
};

// Helper function to generate time slots
const generateTimeSlots = (operatingHours, existingBookings, pricePerHour, durationMinutes = 60, selectedDate = null) => {
  const slots = [];
  const openTime = operatingHours.openTime;
  const closeTime = operatingHours.closeTime;

  const [openHour, openMin] = openTime.split(':').map(Number);
  const [closeHour, closeMin] = closeTime.split(':').map(Number);

  const openMinutes = openHour * 60 + openMin;
  const closeMinutes = closeHour * 60 + closeMin;

  // Get current time in Islamabad timezone
  const now = new Date();
  const islamabadTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Karachi"}));

  // Check if the selected date is today
  const isToday = selectedDate && selectedDate.toDateString() === islamabadTime.toDateString();

  // Create a timeline of booked periods
  const bookedPeriods = existingBookings.map(booking => {
    const [startHour, startMin] = booking.timeSlot.startTime.split(':').map(Number);
    const [endHour, endMin] = booking.timeSlot.endTime.split(':').map(Number);
    return {
      start: startHour * 60 + startMin,
      end: endHour * 60 + endMin
    };
  }).sort((a, b) => a.start - b.start);

  // Generate slots with 30-minute intervals
  for (let time = openMinutes; time + durationMinutes <= closeMinutes; time += 30) {
    const endTime = time + durationMinutes;

    // Skip past time slots for today
    if (isToday) {
      const slotHour = Math.floor(time / 60);
      const slotMinute = time % 60;
      const slotTime = new Date(selectedDate);
      slotTime.setHours(slotHour, slotMinute, 0, 0);

      // Skip if slot time has passed (with 30 minute buffer)
      const bufferTime = new Date(islamabadTime.getTime() + 30 * 60 * 1000);
      if (slotTime <= bufferTime) {
        continue;
      }
    }

    // Check if this slot conflicts with any existing booking
    const hasConflict = bookedPeriods.some(period =>
      (time < period.end && endTime > period.start)
    );

    if (!hasConflict) {
      const startHour = Math.floor(time / 60);
      const startMin = time % 60;
      const endHour = Math.floor(endTime / 60);
      const endMinute = endTime % 60;

      const startTimeStr = `${startHour.toString().padStart(2, '0')}:${startMin.toString().padStart(2, '0')}`;
      const endTimeStr = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

      // Calculate price based on duration using pricing tiers
      const totalPrice = calculatePrice(durationMinutes, operatingHours, pricePerHour);

      slots.push({
        startTime: startTimeStr,
        endTime: endTimeStr,
        durationMinutes,
        price: totalPrice,
        available: true
      });
    }
  }

  return slots;
};
