import UserActivity from '../models/UserActivity.js';

// Track user activity
export const trackActivity = async (req, res) => {
  try {
    const { session, activities } = req.body;

    if (!session || !activities || !Array.isArray(activities)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid activity data'
      });
    }

    // Add IP address and other server-side data
    const enhancedSession = {
      ...session,
      ipAddress: req.ip || req.connection.remoteAddress,
      // You can add geolocation data here if needed
    };

    // Find existing activity record or create new one
    let userActivity = await UserActivity.findOne({
      'session.sessionId': session.sessionId
    });

    if (userActivity) {
      // Append new activities
      userActivity.activities.push(...activities);
      userActivity.session.endTime = new Date();
    } else {
      // Create new activity record
      userActivity = new UserActivity({
        session: enhancedSession,
        activities
      });
    }

    await userActivity.save();

    res.json({
      success: true,
      message: 'Activity tracked successfully'
    });

  } catch (error) {
    console.error('Track activity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track activity'
    });
  }
};

// Get user activity analytics (admin only)
export const getUserActivityAnalytics = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      userId,
      page = 1,
      limit = 50
    } = req.query;

    const filter = {};

    // Date range filter
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    // User filter
    if (userId) {
      filter['session.userId'] = userId;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const activities = await UserActivity.find(filter)
      .populate('session.userId', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await UserActivity.countDocuments(filter);

    res.json({
      success: true,
      data: {
        activities,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get user activity analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch activity analytics'
    });
  }
};

// Get conversion funnel analytics
export const getConversionFunnelAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const filter = {};
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    const funnelData = await UserActivity.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          totalSessions: { $sum: 1 },
          landingPage: { $sum: { $cond: ['$conversionFunnel.landingPage', 1, 0] } },
          courtSelection: { $sum: { $cond: ['$conversionFunnel.courtSelection', 1, 0] } },
          dateSelection: { $sum: { $cond: ['$conversionFunnel.dateSelection', 1, 0] } },
          timeSlotSelection: { $sum: { $cond: ['$conversionFunnel.timeSlotSelection', 1, 0] } },
          bookingForm: { $sum: { $cond: ['$conversionFunnel.bookingForm', 1, 0] } },
          otpVerification: { $sum: { $cond: ['$conversionFunnel.otpVerification', 1, 0] } },
          bookingCompleted: { $sum: { $cond: ['$conversionFunnel.bookingCompleted', 1, 0] } },
          bookingAbandoned: { $sum: { $cond: ['$bookingAbandoned', 1, 0] } }
        }
      }
    ]);

    const data = funnelData[0] || {
      totalSessions: 0,
      landingPage: 0,
      courtSelection: 0,
      dateSelection: 0,
      timeSlotSelection: 0,
      bookingForm: 0,
      otpVerification: 0,
      bookingCompleted: 0,
      bookingAbandoned: 0
    };

    // Calculate conversion rates
    const conversionRates = {
      landingToCourtSelection: data.totalSessions > 0 ? (data.courtSelection / data.totalSessions * 100).toFixed(2) : 0,
      courtToDate: data.courtSelection > 0 ? (data.dateSelection / data.courtSelection * 100).toFixed(2) : 0,
      dateToTimeSlot: data.dateSelection > 0 ? (data.timeSlotSelection / data.dateSelection * 100).toFixed(2) : 0,
      timeSlotToForm: data.timeSlotSelection > 0 ? (data.bookingForm / data.timeSlotSelection * 100).toFixed(2) : 0,
      formToOtp: data.bookingForm > 0 ? (data.otpVerification / data.bookingForm * 100).toFixed(2) : 0,
      otpToCompletion: data.otpVerification > 0 ? (data.bookingCompleted / data.otpVerification * 100).toFixed(2) : 0,
      overallConversion: data.totalSessions > 0 ? (data.bookingCompleted / data.totalSessions * 100).toFixed(2) : 0,
      abandonmentRate: data.totalSessions > 0 ? (data.bookingAbandoned / data.totalSessions * 100).toFixed(2) : 0
    };

    res.json({
      success: true,
      data: {
        funnel: data,
        conversionRates
      }
    });

  } catch (error) {
    console.error('Get conversion funnel analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch conversion funnel analytics'
    });
  }
};

// Get abandonment analytics
export const getAbandonmentAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const filter = { bookingAbandoned: true };
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    const abandonmentData = await UserActivity.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$abandonmentReason',
          count: { $sum: 1 },
          avgSessionDuration: { $avg: '$sessionDuration' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    const totalAbandoned = await UserActivity.countDocuments(filter);

    res.json({
      success: true,
      data: {
        totalAbandoned,
        reasonBreakdown: abandonmentData
      }
    });

  } catch (error) {
    console.error('Get abandonment analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch abandonment analytics'
    });
  }
};

// Get popular time slots and courts
export const getPopularityAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const filter = {};
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    // Get popular courts
    const popularCourts = await UserActivity.aggregate([
      { $match: filter },
      { $unwind: '$activities' },
      { $match: { 'activities.action': 'court_selection' } },
      {
        $group: {
          _id: '$activities.data.courtId',
          selections: { $sum: 1 },
          courtName: { $first: '$activities.data.courtName' }
        }
      },
      { $sort: { selections: -1 } },
      { $limit: 10 }
    ]);

    // Get popular time slots
    const popularTimeSlots = await UserActivity.aggregate([
      { $match: filter },
      { $unwind: '$activities' },
      { $match: { 'activities.action': 'time_slot_selection' } },
      {
        $group: {
          _id: '$activities.data.timeSlot',
          selections: { $sum: 1 }
        }
      },
      { $sort: { selections: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      data: {
        popularCourts,
        popularTimeSlots
      }
    });

  } catch (error) {
    console.error('Get popularity analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch popularity analytics'
    });
  }
};
