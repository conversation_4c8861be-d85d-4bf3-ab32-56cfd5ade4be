import { Booking, Court, OperatingHours, OTP, User } from '../models/index.js';
import { calculatePrice } from '../utils/pricing.js';
import { validationResult } from 'express-validator';
import emailService from '../services/emailService.js';

// Create new booking
export const createBooking = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { courtId, date, timeSlot, durationMinutes = 60 } = req.body;
    const customer = req.user._id;

    // Verify court exists and is available
    const court = await Court.findOne({ _id: courtId, status: 'active' });
    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    // Check if the time slot is available
    // Parse date as local date to avoid timezone issues
    const [year, month, day] = date.split('-').map(Number);
    const bookingDate = new Date(year, month - 1, day); // month is 0-indexed
    const isSlotAvailable = await checkTimeSlotAvailability(courtId, bookingDate, timeSlot);
    
    if (!isSlotAvailable.available) {
      return res.status(400).json({
        success: false,
        message: isSlotAvailable.reason
      });
    }

    // Get operating hours for pricing
    const dayOfWeek = bookingDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const operatingHours = await OperatingHours.findOne({ dayOfWeek });

    // Calculate total price based on duration using pricing tiers
    const totalPrice = calculatePrice(durationMinutes, operatingHours, court.pricePerHour);

    // Create booking
    const booking = new Booking({
      court: courtId,
      customer,
      date: bookingDate,
      timeSlot: {
        startTime: timeSlot.startTime,
        endTime: timeSlot.endTime,
        durationMinutes
      },
      totalPrice,
      status: 'pending'
    });

    await booking.save();

    // Populate court and customer data
    await booking.populate([
      { path: 'court', select: 'name location pricePerHour' },
      { path: 'customer', select: 'firstName lastName email phone' }
    ]);

    // Send booking confirmation email
    try {
      console.log('Sending booking confirmation email to:', booking.customer.email);
      const emailResult = await emailService.sendBookingConfirmation(
        booking.customer.email,
        `${booking.customer.firstName} ${booking.customer.lastName}`,
        {
          court: { name: booking.court.name },
          date: booking.date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
          timeSlot: {
            startTime: booking.timeSlot.startTime,
            endTime: booking.timeSlot.endTime,
            durationMinutes: booking.timeSlot.durationMinutes
          },
          totalPrice: booking.totalPrice,
          status: booking.status.charAt(0).toUpperCase() + booking.status.slice(1)
        }
      );
      console.log('Booking confirmation email result:', emailResult);
    } catch (emailError) {
      console.error('Failed to send booking confirmation email:', emailError);
      // Don't fail the booking if email fails
    }

    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      data: { booking }
    });

  } catch (error) {
    console.error('Create booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create booking',
      error: error.message
    });
  }
};

// Get user's bookings
export const getUserBookings = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      upcoming = false
    } = req.query;

    const filter = { customer: req.user._id };

    // Filter by status
    if (status && status !== 'all') {
      filter.status = status;
    }

    // Filter for upcoming bookings
    if (upcoming === 'true') {
      filter.date = { $gte: new Date() };
      filter.status = { $in: ['pending', 'confirmed'] };
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const bookings = await Booking.find(filter)
      .populate('court', 'name location pricePerHour image')
      .sort({ date: -1, 'timeSlot.startTime': 1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Booking.countDocuments(filter);

    res.json({
      success: true,
      data: {
        bookings,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get user bookings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookings',
      error: error.message
    });
  }
};

// Get all bookings (admin only)
export const getAllBookings = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      court,
      dateFrom,
      dateTo,
      customer
    } = req.query;

    const filter = {};

    // Filter by status
    if (status && status !== 'all') {
      filter.status = status;
    }

    // Filter by court
    if (court && court !== 'all') {
      filter.court = court;
    }

    // Filter by customer
    if (customer) {
      filter.customer = customer;
    }

    // Filter by date range
    if (dateFrom || dateTo) {
      filter.date = {};
      if (dateFrom) filter.date.$gte = new Date(dateFrom);
      if (dateTo) filter.date.$lte = new Date(dateTo);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const bookings = await Booking.find(filter)
      .populate('court', 'name location pricePerHour')
      .populate('customer', 'firstName lastName email phone')
      .sort({ date: -1, 'timeSlot.startTime': 1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Booking.countDocuments(filter);

    res.json({
      success: true,
      data: {
        bookings,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get all bookings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookings',
      error: error.message
    });
  }
};

// Get booking by ID
export const getBookingById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;

    let filter = { _id: id };

    // Non-admin users can only see their own bookings
    if (userRole !== 'admin') {
      filter.customer = userId;
    }

    const booking = await Booking.findOne(filter)
      .populate('court', 'name location pricePerHour image amenities specifications')
      .populate('customer', 'firstName lastName email phone');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    res.json({
      success: true,
      data: { booking }
    });

  } catch (error) {
    console.error('Get booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch booking',
      error: error.message
    });
  }
};

// Update booking status (admin only)
export const updateBookingStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    const validStatuses = ['pending', 'confirmed', 'cancelled', 'completed', 'no-show'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    const booking = await Booking.findById(id);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Update booking
    booking.status = status;
    if (notes) booking.notes = notes;

    // Handle cancellation
    if (status === 'cancelled') {
      booking.cancelledAt = new Date();
      booking.cancelledBy = req.user._id;
    }

    await booking.save();

    await booking.populate([
      { path: 'court', select: 'name location' },
      { path: 'customer', select: 'firstName lastName email' }
    ]);

    res.json({
      success: true,
      message: 'Booking status updated successfully',
      data: { booking }
    });

  } catch (error) {
    console.error('Update booking status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update booking status',
      error: error.message
    });
  }
};

// Cancel booking (customer or admin)
export const cancelBooking = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.user._id;
    const userRole = req.user.role;

    let filter = { _id: id };

    // Non-admin users can only cancel their own bookings
    if (userRole !== 'admin') {
      filter.customer = userId;
    }

    const booking = await Booking.findOne(filter);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if booking can be cancelled
    if (userRole !== 'admin' && !booking.canBeCancelled()) {
      return res.status(400).json({
        success: false,
        message: 'Booking cannot be cancelled. Must be cancelled at least 24 hours before the booking time.'
      });
    }

    if (booking.status === 'cancelled') {
      return res.status(400).json({
        success: false,
        message: 'Booking is already cancelled'
      });
    }

    if (booking.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Cannot cancel completed booking'
      });
    }

    // Update booking
    booking.status = 'cancelled';
    booking.cancelledAt = new Date();
    booking.cancelledBy = userId;
    if (reason) booking.cancellationReason = reason;

    await booking.save();

    await booking.populate([
      { path: 'court', select: 'name location' },
      { path: 'customer', select: 'firstName lastName email' }
    ]);

    res.json({
      success: true,
      message: 'Booking cancelled successfully',
      data: { booking }
    });

  } catch (error) {
    console.error('Cancel booking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel booking',
      error: error.message
    });
  }
};

// Helper function to check time slot availability
const checkTimeSlotAvailability = async (courtId, date, timeSlot) => {
  try {
    // Check if court exists and is active
    const court = await Court.findOne({ _id: courtId, status: 'active' });
    if (!court) {
      return { available: false, reason: 'Court is not available' };
    }

    // Get day of week
    const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

    // Check operating hours
    const operatingHours = await OperatingHours.findOne({ dayOfWeek });
    if (!operatingHours || !operatingHours.isOpen) {
      return { available: false, reason: 'Facility is closed on this day' };
    }

    // Check if time slot is within operating hours
    if (!operatingHours.isOpen) {
      return { available: false, reason: 'Time slot is outside operating hours' };
    }



    // Check for existing bookings
    const existingBooking = await Booking.findOne({
      court: courtId,
      date: {
        $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
        $lt: new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1)
      },
      'timeSlot.startTime': timeSlot.startTime,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingBooking) {
      return { available: false, reason: 'Time slot is already booked' };
    }

    return { available: true };

  } catch (error) {
    console.error('Check availability error:', error);
    return { available: false, reason: 'Error checking availability' };
  }
};

// Send booking OTP
export const sendBookingOTP = async (req, res) => {
  try {
    const { courtId, date, timeSlot } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!courtId || !date || !timeSlot) {
      return res.status(400).json({
        success: false,
        message: 'Court ID, date, and time slot are required'
      });
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get court details
    const court = await Court.findOne({ _id: courtId, status: 'active' });
    if (!court) {
      return res.status(404).json({
        success: false,
        message: 'Court not found'
      });
    }

    // Generate OTP
    const otp = OTP.generateOTP();

    // Save OTP to database
    await OTP.findOneAndUpdate(
      { email: user.email, type: 'booking_verification' },
      {
        email: user.email,
        otp,
        type: 'booking_verification',
        expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
        isUsed: false,
        attempts: 0,
        metadata: {
          courtId,
          date,
          timeSlot,
          userId
        }
      },
      { upsert: true, new: true }
    );

    // Send OTP email
    const emailResult = await emailService.sendBookingOTP(
      user.email,
      `${user.firstName} ${user.lastName}`,
      otp,
      court.name,
      date,
      `${timeSlot.startTime} - ${timeSlot.endTime}`
    );

    if (!emailResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to send verification email'
      });
    }

    res.json({
      success: true,
      message: 'Booking verification OTP sent to your email'
    });

  } catch (error) {
    console.error('Send booking OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Verify booking OTP and create booking
export const verifyBookingOTP = async (req, res) => {
  try {
    const { otp, courtId, date, timeSlot, durationMinutes } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!otp || !courtId || !date || !timeSlot || !durationMinutes) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Verify OTP
    const otpRecord = await OTP.findOne({
      email: user.email,
      type: 'booking_verification',
      otp,
      isUsed: false,
      expiresAt: { $gt: new Date() }
    });

    if (!otpRecord) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }

    // Mark OTP as used
    otpRecord.isUsed = true;
    await otpRecord.save();

    // Create the booking (reuse existing createBooking logic)
    const bookingData = {
      courtId,
      date,
      timeSlot,
      durationMinutes
    };

    // Call the existing booking creation logic
    req.body = bookingData;
    await createBooking(req, res);

  } catch (error) {
    console.error('Verify booking OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Send cancellation OTP
export const sendCancellationOTP = async (req, res) => {
  try {
    const { bookingId } = req.body;
    const userId = req.user._id;

    // Validate required fields
    if (!bookingId) {
      return res.status(400).json({
        success: false,
        message: 'Booking ID is required'
      });
    }

    // Get booking details
    const booking = await Booking.findOne({
      _id: bookingId,
      customer: userId,
      status: { $in: ['pending', 'confirmed'] }
    }).populate('court', 'name').populate('customer', 'firstName lastName email');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found or cannot be cancelled'
      });
    }

    // Check if booking can be cancelled (at least 2 hours before)
    // Use Islamabad timezone for consistent time checking
    const now = new Date();
    const islamabadTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Karachi"}));

    const bookingDateTime = new Date(`${booking.date.toISOString().split('T')[0]}T${booking.timeSlot.startTime}`);
    const hoursUntilBooking = (bookingDateTime - islamabadTime) / (1000 * 60 * 60);

    if (hoursUntilBooking < 2) {
      return res.status(400).json({
        success: false,
        message: 'Bookings can only be cancelled at least 2 hours in advance'
      });
    }

    // Generate OTP
    const otp = OTP.generateOTP();

    // Save OTP to database
    await OTP.findOneAndUpdate(
      { email: booking.customer.email, type: 'booking_cancellation' },
      {
        email: booking.customer.email,
        otp,
        type: 'booking_cancellation',
        expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
        isUsed: false,
        attempts: 0,
        metadata: {
          bookingId,
          userId
        }
      },
      { upsert: true, new: true }
    );

    // Send OTP email
    const emailResult = await emailService.sendCancellationOTP(
      booking.customer.email,
      `${booking.customer.firstName} ${booking.customer.lastName}`,
      otp,
      booking.court.name,
      booking.date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      `${booking.timeSlot.startTime} - ${booking.timeSlot.endTime}`
    );

    if (!emailResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to send cancellation verification email'
      });
    }

    res.json({
      success: true,
      message: 'Cancellation verification OTP sent to your email'
    });

  } catch (error) {
    console.error('Send cancellation OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Verify cancellation OTP and cancel booking
export const verifyCancellationOTP = async (req, res) => {
  try {
    const { otp, bookingId } = req.body;
    const userId = req.user._id;

    // Validate required fields
    if (!otp || !bookingId) {
      return res.status(400).json({
        success: false,
        message: 'OTP and booking ID are required'
      });
    }

    // Get booking details
    const booking = await Booking.findOne({
      _id: bookingId,
      customer: userId,
      status: { $in: ['pending', 'confirmed'] }
    }).populate('court', 'name').populate('customer', 'firstName lastName email');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found or cannot be cancelled'
      });
    }

    // Verify OTP
    const otpRecord = await OTP.findOne({
      email: booking.customer.email,
      type: 'booking_cancellation',
      otp,
      isUsed: false,
      expiresAt: { $gt: new Date() }
    });

    if (!otpRecord) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }

    // Mark OTP as used
    otpRecord.isUsed = true;
    await otpRecord.save();

    // Cancel the booking
    booking.status = 'cancelled';
    booking.cancelledAt = new Date();
    booking.cancelledBy = userId;
    await booking.save();

    // Send cancellation confirmation email
    try {
      await emailService.sendCancellationConfirmation(
        booking.customer.email,
        `${booking.customer.firstName} ${booking.customer.lastName}`,
        {
          court: { name: booking.court.name },
          date: booking.date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
          timeSlot: {
            startTime: booking.timeSlot.startTime,
            endTime: booking.timeSlot.endTime
          },
          totalPrice: booking.totalPrice
        }
      );
    } catch (emailError) {
      console.error('Failed to send cancellation confirmation email:', emailError);
      // Don't fail the cancellation if email fails
    }

    res.json({
      success: true,
      message: 'Booking cancelled successfully',
      data: { booking }
    });

  } catch (error) {
    console.error('Verify cancellation OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
