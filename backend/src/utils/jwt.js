import jwt from 'jsonwebtoken';
import config from '../config/index.js';

// Generate access token
export const generateAccessToken = (payload) => {
  return jwt.sign(payload, config.JWT_SECRET, {
    expiresIn: config.JWT_EXPIRE,
    issuer: 'padel-chase-api',
    audience: 'padel-chase-app'
  });
};

// Generate refresh token
export const generateRefreshToken = (payload) => {
  return jwt.sign(payload, config.JWT_REFRESH_SECRET, {
    expiresIn: config.JWT_REFRESH_EXPIRE,
    issuer: 'padel-chase-api',
    audience: 'padel-chase-app'
  });
};

// Verify access token
export const verifyAccessToken = (token) => {
  try {
    return jwt.verify(token, config.JWT_SECRET, {
      issuer: 'padel-chase-api',
      audience: 'padel-chase-app'
    });
  } catch (error) {
    throw new Error('Invalid or expired access token');
  }
};

// Verify refresh token
export const verifyRefreshToken = (token) => {
  try {
    return jwt.verify(token, config.JWT_REFRESH_SECRET, {
      issuer: 'padel-chase-api',
      audience: 'padel-chase-app'
    });
  } catch (error) {
    throw new Error('Invalid or expired refresh token');
  }
};

// Generate token pair
export const generateTokenPair = (user) => {
  const payload = {
    userId: user._id,
    email: user.email,
    role: user.role
  };

  const accessToken = generateAccessToken(payload);
  const refreshToken = generateRefreshToken({ userId: user._id });

  return { accessToken, refreshToken };
};
