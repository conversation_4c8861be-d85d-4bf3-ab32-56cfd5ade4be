/**
 * Calculate pricing based on duration and pricing tiers
 * @param {number} durationMinutes - Duration in minutes
 * @param {object} operatingHours - Operating hours object with pricing tiers
 * @param {number} basePricePerHour - Base price per hour from court
 * @returns {number} - Calculated price
 */
export const calculatePrice = (durationMinutes, operatingHours, basePricePerHour) => {
  // Default pricing if no operating hours pricing is available
  const defaultPricing = {
    thirtyMinutes: basePricePerHour * 0.6, // 60% of hourly rate
    oneHour: basePricePerHour,
    twoHours: basePricePerHour * 1.8, // 10% discount for 2 hours
    longDuration: basePricePerHour * 0.9 // 10% discount per hour for long bookings
  };

  // Use operating hours pricing if available, otherwise use defaults
  const pricing = {
    thirtyMinutes: operatingHours?.pricingTiers?.thirtyMinutes || defaultPricing.thirtyMinutes,
    oneHour: operatingHours?.pricingTiers?.oneHour || defaultPricing.oneHour,
    twoHours: operatingHours?.pricingTiers?.twoHours || defaultPricing.twoHours,
    longDuration: operatingHours?.pricingTiers?.longDuration || defaultPricing.longDuration
  };

  // Calculate price based on duration with mixed pricing logic
  if (durationMinutes <= 60) {
    // For durations up to 1 hour, use proportional pricing based on 30-min rate
    if (durationMinutes <= 30) {
      // 30 minutes or less - proportional to 30-min rate
      return Math.round(pricing.thirtyMinutes * (durationMinutes / 30));
    } else {
      // 31-60 minutes - 30-min rate + proportional additional time
      const basePrice = pricing.thirtyMinutes; // Full 30-min price
      const additionalMinutes = durationMinutes - 30;
      const additionalPrice = (pricing.thirtyMinutes / 30) * additionalMinutes; // Same rate per minute
      return Math.round(basePrice + additionalPrice);
    }
  } else {
    // For durations over 1 hour, use per-hour pricing
    const hours = Math.floor(durationMinutes / 60);
    const remainingMinutes = durationMinutes % 60;

    if (durationMinutes <= 120) {
      // 1-2 hours - use oneHour rate per hour
      const fullHoursPrice = hours * pricing.oneHour;
      const partialHourPrice = remainingMinutes > 0 ? (pricing.oneHour / 60) * remainingMinutes : 0;
      return Math.round(fullHoursPrice + partialHourPrice);
    } else {
      // More than 2 hours - use twoHours rate for first 2 hours, then longDuration rate
      const firstTwoHoursPrice = pricing.twoHours;
      const additionalHours = (durationMinutes - 120) / 60;
      const additionalPrice = additionalHours * pricing.longDuration;
      return Math.round(firstTwoHoursPrice + additionalPrice);
    }
  }
};

/**
 * Get pricing tiers for display
 * @param {object} operatingHours - Operating hours object with pricing tiers
 * @param {number} basePricePerHour - Base price per hour from court
 * @returns {object} - Pricing tiers object
 */
export const getPricingTiers = (operatingHours, basePricePerHour) => {
  const defaultPricing = {
    thirtyMinutes: basePricePerHour * 0.6,
    oneHour: basePricePerHour,
    twoHours: basePricePerHour * 1.8,
    longDuration: basePricePerHour * 0.9
  };

  return {
    thirtyMinutes: operatingHours?.pricingTiers?.thirtyMinutes || defaultPricing.thirtyMinutes,
    oneHour: operatingHours?.pricingTiers?.oneHour || defaultPricing.oneHour,
    twoHours: operatingHours?.pricingTiers?.twoHours || defaultPricing.twoHours,
    longDuration: operatingHours?.pricingTiers?.longDuration || defaultPricing.longDuration
  };
};

/**
 * Format duration for display
 * @param {number} minutes - Duration in minutes
 * @returns {string} - Formatted duration string
 */
export const formatDuration = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours === 0) {
    return `${mins} min`;
  } else if (mins === 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  } else {
    return `${hours}h ${mins}m`;
  }
};

/**
 * Get price per minute rate for a given duration
 * @param {number} durationMinutes - Duration in minutes
 * @param {object} operatingHours - Operating hours object with pricing tiers
 * @param {number} basePricePerHour - Base price per hour from court
 * @returns {number} - Price per minute
 */
export const getPricePerMinute = (durationMinutes, operatingHours, basePricePerHour) => {
  const totalPrice = calculatePrice(durationMinutes, operatingHours, basePricePerHour);
  return totalPrice / durationMinutes;
};
