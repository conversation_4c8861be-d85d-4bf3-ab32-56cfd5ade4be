import mongoose from 'mongoose';

const activitySchema = new mongoose.Schema({
  timestamp: {
    type: Date,
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      'page_view',
      'booking_flow',
      'booking_abandonment',
      'form_interaction',
      'button_click',
      'error',
      'search',
      'filter_change',
      'court_selection',
      'time_slot_selection',
      'date_selection',
      'booking_completion',
      'payment_attempt',
      'login_attempt',
      'logout',
      'registration_attempt'
    ]
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  url: {
    type: String,
    required: true
  }
});

const sessionSchema = new mongoose.Schema({
  sessionId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false // Can be null for anonymous users
  },
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date,
    default: null
  },
  userAgent: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  },
  referrer: {
    type: String,
    default: ''
  },
  screenResolution: {
    type: String,
    required: true
  },
  viewport: {
    type: String,
    required: true
  },
  timezone: {
    type: String,
    required: true
  },
  language: {
    type: String,
    required: true
  },
  ipAddress: {
    type: String,
    required: false
  },
  country: {
    type: String,
    required: false
  },
  city: {
    type: String,
    required: false
  }
});

const userActivitySchema = new mongoose.Schema({
  session: {
    type: sessionSchema,
    required: true
  },
  activities: [activitySchema],
  totalActivities: {
    type: Number,
    default: 0
  },
  sessionDuration: {
    type: Number, // in seconds
    default: 0
  },
  bookingCompleted: {
    type: Boolean,
    default: false
  },
  bookingAbandoned: {
    type: Boolean,
    default: false
  },
  abandonmentReason: {
    type: String,
    required: false
  },
  conversionFunnel: {
    landingPage: { type: Boolean, default: false },
    courtSelection: { type: Boolean, default: false },
    dateSelection: { type: Boolean, default: false },
    timeSlotSelection: { type: Boolean, default: false },
    bookingForm: { type: Boolean, default: false },
    otpVerification: { type: Boolean, default: false },
    bookingCompleted: { type: Boolean, default: false }
  },
  errors: [{
    timestamp: Date,
    message: String,
    stack: String,
    context: mongoose.Schema.Types.Mixed
  }]
}, {
  timestamps: true
});

// Indexes for better query performance
userActivitySchema.index({ 'session.userId': 1, createdAt: -1 });
userActivitySchema.index({ 'session.sessionId': 1 });
userActivitySchema.index({ createdAt: -1 });
userActivitySchema.index({ bookingCompleted: 1 });
userActivitySchema.index({ bookingAbandoned: 1 });

// Methods
userActivitySchema.methods.updateConversionFunnel = function() {
  const activities = this.activities;
  
  activities.forEach(activity => {
    switch (activity.action) {
      case 'page_view':
        if (activity.data.page === '/') {
          this.conversionFunnel.landingPage = true;
        }
        break;
      case 'booking_flow':
        switch (activity.data.step) {
          case 'court_selection':
            this.conversionFunnel.courtSelection = true;
            break;
          case 'date_selection':
            this.conversionFunnel.dateSelection = true;
            break;
          case 'time_slot_selection':
            this.conversionFunnel.timeSlotSelection = true;
            break;
          case 'booking_form':
            this.conversionFunnel.bookingForm = true;
            break;
          case 'otp_verification':
            this.conversionFunnel.otpVerification = true;
            break;
          case 'booking_completed':
            this.conversionFunnel.bookingCompleted = true;
            this.bookingCompleted = true;
            break;
        }
        break;
      case 'booking_abandonment':
        this.bookingAbandoned = true;
        this.abandonmentReason = activity.data.reason;
        break;
      case 'error':
        this.errors.push({
          timestamp: activity.timestamp,
          message: activity.data.message,
          stack: activity.data.stack,
          context: activity.data.context
        });
        break;
    }
  });
};

userActivitySchema.methods.calculateSessionDuration = function() {
  if (this.activities.length > 0) {
    const firstActivity = this.activities[0];
    const lastActivity = this.activities[this.activities.length - 1];
    
    this.sessionDuration = (
      new Date(lastActivity.timestamp) - new Date(firstActivity.timestamp)
    ) / 1000;
  }
};

// Pre-save middleware
userActivitySchema.pre('save', function(next) {
  this.totalActivities = this.activities.length;
  this.updateConversionFunnel();
  this.calculateSessionDuration();
  next();
});

const UserActivity = mongoose.model('UserActivity', userActivitySchema);

export default UserActivity;
