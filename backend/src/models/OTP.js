import mongoose from 'mongoose';

const otpSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    lowercase: true,
    trim: true
  },
  otp: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['email_verification', 'password_reset', 'booking_verification', 'payment_verification', 'booking_cancellation'],
    required: true
  },
  expiresAt: {
    type: Date,
    required: true,
    default: () => new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
  },
  isUsed: {
    type: Boolean,
    default: false
  },
  attempts: {
    type: Number,
    default: 0,
    max: 5
  }
}, {
  timestamps: true
});

// Index for automatic cleanup of expired OTPs
otpSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Index for efficient queries
otpSchema.index({ email: 1, type: 1 });

// Method to generate OTP
otpSchema.statics.generateOTP = function() {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
};

// Method to verify OTP
otpSchema.methods.verify = function(inputOtp) {
  if (this.isUsed) {
    return { success: false, message: 'OTP has already been used' };
  }
  
  if (this.expiresAt < new Date()) {
    return { success: false, message: 'OTP has expired' };
  }
  
  if (this.attempts >= 5) {
    return { success: false, message: 'Too many attempts. Please request a new OTP' };
  }
  
  this.attempts += 1;
  
  if (this.otp !== inputOtp) {
    return { success: false, message: 'Invalid OTP' };
  }
  
  this.isUsed = true;
  return { success: true, message: 'OTP verified successfully' };
};

const OTP = mongoose.model('OTP', otpSchema);

export default OTP;
