import mongoose from 'mongoose';

const reminderLogSchema = new mongoose.Schema({
  bookingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reminderType: {
    type: String,
    required: true,
    enum: ['one_hour_before', 'one_day_before', 'booking_confirmation', 'cancellation_confirmation'],
    default: 'one_hour_before'
  },
  sentAt: {
    type: Date,
    required: true,
    default: Date.now
  },
  email: {
    type: String,
    required: true
  },
  bookingDate: {
    type: Date,
    required: true
  },
  bookingTime: {
    type: String,
    required: true
  },
  emailStatus: {
    type: String,
    enum: ['sent', 'failed', 'pending'],
    default: 'sent'
  },
  errorMessage: {
    type: String,
    required: false
  },
  retryCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Compound index to prevent duplicate reminders
reminderLogSchema.index({ 
  bookingId: 1, 
  reminderType: 1 
}, { unique: true });

// Index for efficient querying
reminderLogSchema.index({ sentAt: -1 });
reminderLogSchema.index({ bookingDate: 1, reminderType: 1 });

// Static method to check if reminder was already sent
reminderLogSchema.statics.wasReminderSent = async function(bookingId, reminderType) {
  const reminder = await this.findOne({ bookingId, reminderType });
  return !!reminder;
};

// Static method to log a sent reminder
reminderLogSchema.statics.logReminder = async function(bookingId, userId, reminderType, email, bookingDate, bookingTime, emailStatus = 'sent', errorMessage = null) {
  try {
    const reminder = new this({
      bookingId,
      userId,
      reminderType,
      email,
      bookingDate,
      bookingTime,
      emailStatus,
      errorMessage
    });
    
    await reminder.save();
    return reminder;
  } catch (error) {
    // If duplicate key error (reminder already sent), that's okay
    if (error.code === 11000) {
      console.log(`Reminder ${reminderType} for booking ${bookingId} already sent`);
      return null;
    }
    throw error;
  }
};

const ReminderLog = mongoose.model('ReminderLog', reminderLogSchema);

export default ReminderLog;
