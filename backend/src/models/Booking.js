import mongoose from 'mongoose';

const bookingSchema = new mongoose.Schema({
  court: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Court',
    required: [true, 'Court is required']
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  date: {
    type: Date,
    required: [true, 'Booking date is required']
  },
  timeSlot: {
    startTime: {
      type: String,
      required: [true, 'Start time is required'],
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
    },
    endTime: {
      type: String,
      required: [true, 'End time is required'],
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
    },
    durationMinutes: {
      type: Number,
      required: true,
      min: [30, 'Minimum booking duration is 30 minutes'],
      max: [480, 'Maximum booking duration is 8 hours'],
      default: 60
    }
  },
  totalPrice: {
    type: Number,
    required: [true, 'Total price is required'],
    min: [0, 'Price cannot be negative']
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'cancelled', 'completed', 'no-show'],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'refunded', 'failed'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['card', 'cash', 'bank_transfer', 'online'],
    default: 'card'
  },
  paymentDetails: {
    transactionId: String,
    paymentDate: Date,
    refundId: String,
    refundDate: Date,
    refundAmount: Number
  },
  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot exceed 1000 characters']
  },
  cancellationReason: {
    type: String,
    maxlength: [500, 'Cancellation reason cannot exceed 500 characters']
  },
  cancelledAt: {
    type: Date
  },
  cancelledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reminderSent: {
    type: Boolean,
    default: false
  },
  checkInTime: {
    type: Date
  },
  checkOutTime: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for booking duration in minutes
bookingSchema.virtual('durationMinutes').get(function() {
  if (this.timeSlot.startTime && this.timeSlot.endTime) {
    const [startHour, startMin] = this.timeSlot.startTime.split(':').map(Number);
    const [endHour, endMin] = this.timeSlot.endTime.split(':').map(Number);
    return (endHour * 60 + endMin) - (startHour * 60 + startMin);
  }
  return 0;
});

// Virtual for full booking date and time
bookingSchema.virtual('fullDateTime').get(function() {
  const date = new Date(this.date);
  return `${date.toDateString()} ${this.timeSlot.startTime} - ${this.timeSlot.endTime}`;
});

// Indexes for efficient queries
bookingSchema.index({ court: 1, date: 1, 'timeSlot.startTime': 1 });
bookingSchema.index({ customer: 1, date: -1 });
bookingSchema.index({ status: 1 });
bookingSchema.index({ date: 1, status: 1 });

// Method to check if booking can be cancelled
bookingSchema.methods.canBeCancelled = function() {
  const now = new Date();
  const bookingDateTime = new Date(this.date);
  const [hour, minute] = this.timeSlot.startTime.split(':').map(Number);
  bookingDateTime.setHours(hour, minute, 0, 0);
  
  const hoursUntilBooking = (bookingDateTime - now) / (1000 * 60 * 60);
  return hoursUntilBooking >= 24 && this.status === 'confirmed';
};

// Method to check if booking is upcoming
bookingSchema.methods.isUpcoming = function() {
  const now = new Date();
  const bookingDate = new Date(this.date);
  return bookingDate >= now && (this.status === 'confirmed' || this.status === 'pending');
};

const Booking = mongoose.model('Booking', bookingSchema);

export default Booking;
