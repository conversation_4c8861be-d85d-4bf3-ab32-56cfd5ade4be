import mongoose from 'mongoose';

const operatingHoursSchema = new mongoose.Schema({
  dayOfWeek: {
    type: String,
    enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
    required: true,
    unique: true
  },
  isOpen: {
    type: Boolean,
    default: true
  },
  openTime: {
    type: String,
    required: function() { return this.isOpen; },
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
  },
  closeTime: {
    type: String,
    required: function() { return this.isOpen; },
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
  },
  // add price per hours so that we can override the price for booking on low demand days
  pricePerHour: {
    type: Number,
    min: [0, 'Price cannot be negative']
  },
  // Pricing tiers for different durations
  pricingTiers: {
    thirtyMinutes: {
      type: Number,
      min: [0, 'Price cannot be negative']
    },
    oneHour: {
      type: Number,
      min: [0, 'Price cannot be negative']
    },
    twoHours: {
      type: Number,
      min: [0, 'Price cannot be negative']
    },
    longDuration: { // Per hour rate for 2+ hours
      type: Number,
      min: [0, 'Price cannot be negative']
    }
  },
  breaks: [{
    startTime: {
      type: String,
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
    },
    endTime: {
      type: String,
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)']
    },
    reason: {
      type: String,
      maxlength: [100, 'Break reason cannot exceed 100 characters']
    }
  }],
  specialNotes: {
    type: String,
    maxlength: [500, 'Special notes cannot exceed 500 characters']
  }
}, {
  timestamps: true
});

// Method to check if facility is open at a specific time
operatingHoursSchema.methods.isOpenAt = function(time) {
  if (!this.isOpen) return false;
  
  const timeMinutes = this.timeToMinutes(time);
  const openMinutes = this.timeToMinutes(this.openTime);
  const closeMinutes = this.timeToMinutes(this.closeTime);
  
  // Check if time is within operating hours
  if (timeMinutes < openMinutes || timeMinutes >= closeMinutes) {
    return false;
  }
  
  // Check if time is during a break
  for (const breakPeriod of this.breaks) {
    const breakStart = this.timeToMinutes(breakPeriod.startTime);
    const breakEnd = this.timeToMinutes(breakPeriod.endTime);
    if (timeMinutes >= breakStart && timeMinutes < breakEnd) {
      return false;
    }
  }
  
  return true;
};

// Helper method to convert time string to minutes
operatingHoursSchema.methods.timeToMinutes = function(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
};

// Method to get available time slots for the day
operatingHoursSchema.methods.getAvailableSlots = function(slotDuration = 60) {
  if (!this.isOpen) return [];
  
  const slots = [];
  const openMinutes = this.timeToMinutes(this.openTime);
  const closeMinutes = this.timeToMinutes(this.closeTime);
  
  for (let time = openMinutes; time < closeMinutes; time += slotDuration) {
    const timeString = this.minutesToTime(time);
    const endTimeString = this.minutesToTime(time + slotDuration);
    
    // Check if this slot conflicts with any breaks
    let isAvailable = true;
    for (const breakPeriod of this.breaks) {
      const breakStart = this.timeToMinutes(breakPeriod.startTime);
      const breakEnd = this.timeToMinutes(breakPeriod.endTime);
      
      if ((time >= breakStart && time < breakEnd) || 
          (time + slotDuration > breakStart && time + slotDuration <= breakEnd) ||
          (time < breakStart && time + slotDuration > breakEnd)) {
        isAvailable = false;
        break;
      }
    }
    
    if (isAvailable && time + slotDuration <= closeMinutes) {
      slots.push({
        startTime: timeString,
        endTime: endTimeString
      });
    }
  }
  
  return slots;
};

// Helper method to convert minutes to time string
operatingHoursSchema.methods.minutesToTime = function(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

const OperatingHours = mongoose.model('OperatingHours', operatingHoursSchema);

export default OperatingHours;
