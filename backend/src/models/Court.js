import mongoose from "mongoose";

const courtSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Court name is required"],
      trim: true,
      maxlength: [100, "Court name cannot exceed 100 characters"],
    },
    description: {
      type: String,
      required: [true, "Court description is required"],
      trim: true,
      maxlength: [1000, "Description cannot exceed 1000 characters"],
    },
    image: {
      type: String,
      default: null,
    },
    capacity: {
      type: Number,
      required: [true, "Court capacity is required"],
      min: [2, "Capacity must be at least 2"],
      max: [6, "Capacity cannot exceed 6"],
      default: 4,
    },
    pricePerHour: {
      type: Number,
      required: [true, "Price per hour is required"],
      min: [0, "Price cannot be negative"],
    },
    status: {
      type: String,
      enum: ["active", "inactive", "maintenance"],
      default: "active",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

const Court = mongoose.model("Court", courtSchema);

export default Court;
