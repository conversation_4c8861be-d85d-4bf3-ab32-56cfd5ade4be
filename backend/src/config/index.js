import dotenv from 'dotenv';

dotenv.config();

const config = {
  // Server
  PORT: process.env.PORT || 5000,
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // Database
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/padel-chase',
  
  // JWT
  JWT_SECRET: process.env.JWT_SECRET || 'fallback-secret-key',
  JWT_EXPIRE: process.env.JWT_EXPIRE || '7d',
  JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret-key',
  JWT_REFRESH_EXPIRE: process.env.JWT_REFRESH_EXPIRE || '30d',
  
  // Email (Mailjet)
  MAILJET_API_KEY: process.env.MAILJET_API_KEY,
  MAILJET_API_SECRET: process.env.MAILJET_API_SECRET,
  FROM_EMAIL: process.env.FROM_EMAIL || '<EMAIL>',
  FROM_NAME: process.env.FROM_NAME || 'Padel Chase',
  
  // File Upload
  MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE) || 5242880, // 5MB
  UPLOAD_PATH: process.env.UPLOAD_PATH || './uploads',
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  
  // CORS
  CORS_ORIGIN: process.env.CORS_ORIGIN || 'https://www.padelchase.pk',
  
  // Booking
  BOOKING_ADVANCE_DAYS: parseInt(process.env.BOOKING_ADVANCE_DAYS) || 30,
  BOOKING_CANCELLATION_HOURS: parseInt(process.env.BOOKING_CANCELLATION_HOURS) || 24,
  DEFAULT_BOOKING_DURATION: parseInt(process.env.DEFAULT_BOOKING_DURATION) || 60,
  
  // Payment
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
  STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
};

export default config;
