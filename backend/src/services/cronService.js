import cron from 'node-cron';
import { Booking } from '../models/index.js';
import ReminderLog from '../models/ReminderLog.js';
import emailService from './emailService.js';

class CronService {
  constructor() {
    this.jobs = new Map();
    this.isRunning = false;
  }

  // Start all cron jobs
  start() {
    if (this.isRunning) {
      console.log('Cron service is already running');
      return;
    }

    console.log('Starting cron service...');
    this.isRunning = true;

    // Run booking reminders every hour at minute 0
    const reminderJob = cron.schedule('0 * * * *', async () => {
      console.log('Running hourly booking reminder job...');
      await this.sendBookingReminders();
    }, {
      scheduled: false,
      timezone: 'Asia/Karachi' // Adjust to your timezone
    });

    this.jobs.set('bookingReminders', reminderJob);
    reminderJob.start();

    // Optional: Run a cleanup job daily at 2 AM to remove old reminder logs
    const cleanupJob = cron.schedule('0 2 * * *', async () => {
      console.log('Running daily cleanup job...');
      await this.cleanupOldReminders();
    }, {
      scheduled: false,
      timezone: 'Asia/Karachi'
    });

    this.jobs.set('cleanup', cleanupJob);
    cleanupJob.start();

    console.log('Cron service started successfully');
  }

  // Stop all cron jobs
  stop() {
    if (!this.isRunning) {
      console.log('Cron service is not running');
      return;
    }

    console.log('Stopping cron service...');
    
    this.jobs.forEach((job, name) => {
      job.stop();
      console.log(`Stopped ${name} job`);
    });

    this.jobs.clear();
    this.isRunning = false;
    console.log('Cron service stopped');
  }

  // Send booking reminders for bookings starting in the next hour
  async sendBookingReminders() {
    try {
      const now = new Date();
      const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
      const twoHoursFromNow = new Date(now.getTime() + 2 * 60 * 60 * 1000);

      console.log(`Checking for bookings between ${oneHourFromNow.toISOString()} and ${twoHoursFromNow.toISOString()}`);

      // Find bookings that start in the next hour
      const upcomingBookings = await Booking.find({
        status: { $in: ['pending', 'confirmed'] },
        date: {
          $gte: new Date(now.toDateString()), // Today or later
          $lte: new Date(oneHourFromNow.toDateString()) // Today only for now
        }
      })
      .populate('customer', 'firstName lastName email')
      .populate('court', 'name location');

      console.log(`Found ${upcomingBookings.length} potential bookings for reminders`);

      let remindersSent = 0;
      let remindersSkipped = 0;
      let remindersFailed = 0;

      for (const booking of upcomingBookings) {
        try {
          // Check if this booking's time is within the next hour
          const bookingDateTime = this.getBookingDateTime(booking);
          const timeDiff = bookingDateTime.getTime() - now.getTime();
          const hoursUntilBooking = timeDiff / (1000 * 60 * 60);

          // Only send reminder if booking is between 0.5 and 1.5 hours away
          if (hoursUntilBooking < 0.5 || hoursUntilBooking > 1.5) {
            continue;
          }

          // Check if reminder was already sent
          const alreadySent = await ReminderLog.wasReminderSent(booking._id, 'one_hour_before');
          if (alreadySent) {
            console.log(`Reminder already sent for booking ${booking._id}`);
            remindersSkipped++;
            continue;
          }

          // Send reminder email
          const emailResult = await emailService.sendBookingReminder(
            booking.customer.email,
            `${booking.customer.firstName} ${booking.customer.lastName}`,
            {
              court: { name: booking.court.name },
              date: booking.date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }),
              timeSlot: {
                startTime: booking.timeSlot.startTime,
                endTime: booking.timeSlot.endTime,
                durationMinutes: booking.timeSlot.durationMinutes
              }
            }
          );

          // Log the reminder
          if (emailResult.success) {
            await ReminderLog.logReminder(
              booking._id,
              booking.customer._id,
              'one_hour_before',
              booking.customer.email,
              booking.date,
              booking.timeSlot.startTime,
              'sent'
            );
            remindersSent++;
            console.log(`Reminder sent for booking ${booking._id} to ${booking.customer.email}`);
          } else {
            await ReminderLog.logReminder(
              booking._id,
              booking.customer._id,
              'one_hour_before',
              booking.customer.email,
              booking.date,
              booking.timeSlot.startTime,
              'failed',
              emailResult.error?.message || 'Unknown error'
            );
            remindersFailed++;
            console.error(`Failed to send reminder for booking ${booking._id}:`, emailResult.error);
          }

        } catch (error) {
          console.error(`Error processing reminder for booking ${booking._id}:`, error);
          remindersFailed++;
          
          // Log failed reminder
          try {
            await ReminderLog.logReminder(
              booking._id,
              booking.customer._id,
              'one_hour_before',
              booking.customer.email,
              booking.date,
              booking.timeSlot.startTime,
              'failed',
              error.message
            );
          } catch (logError) {
            console.error('Failed to log reminder error:', logError);
          }
        }
      }

      console.log(`Reminder job completed: ${remindersSent} sent, ${remindersSkipped} skipped, ${remindersFailed} failed`);

    } catch (error) {
      console.error('Error in sendBookingReminders:', error);
    }
  }

  // Helper method to get booking date and time as a Date object
  getBookingDateTime(booking) {
    const bookingDate = new Date(booking.date);
    const [hours, minutes] = booking.timeSlot.startTime.split(':').map(Number);
    
    const bookingDateTime = new Date(bookingDate);
    bookingDateTime.setHours(hours, minutes, 0, 0);
    
    return bookingDateTime;
  }

  // Clean up old reminder logs (older than 30 days)
  async cleanupOldReminders() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const result = await ReminderLog.deleteMany({
        sentAt: { $lt: thirtyDaysAgo }
      });

      console.log(`Cleaned up ${result.deletedCount} old reminder logs`);
    } catch (error) {
      console.error('Error in cleanupOldReminders:', error);
    }
  }

  // Manual trigger for testing
  async triggerBookingReminders() {
    console.log('Manually triggering booking reminders...');
    await this.sendBookingReminders();
  }

  // Get status of cron service
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeJobs: Array.from(this.jobs.keys()),
      jobCount: this.jobs.size
    };
  }
}

// Create singleton instance
const cronService = new CronService();

export default cronService;
