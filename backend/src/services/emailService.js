import axios from 'axios';
import config from '../config/index.js';

const MAILJET_API_URL = 'https://api.mailjet.com/v3.1/send';

class EmailService {
  constructor() {
    this.apiKey = config.MAILJET_API_KEY;
    this.apiSecret = config.MAILJET_API_SECRET;
    this.fromEmail = config.FROM_EMAIL || '<EMAIL>';
    this.fromName = config.FROM_NAME || 'Padel Chase';
  }

  async sendEmail(to, subject, htmlContent, textContent = '') {
    try {
      console.log('Attempting to send email to:', to.email, 'Subject:', subject);

      if (!this.apiKey || !this.apiSecret) {
        console.error('Mailjet API credentials not configured');
        return { success: false, error: 'Email service not configured' };
      }

      const auth = Buffer.from(`${this.apiKey}:${this.apiSecret}`).toString('base64');

      const response = await axios.post(MAILJET_API_URL, {
        Messages: [
          {
            From: {
              Email: this.fromEmail,
              Name: this.fromName
            },
            To: [
              {
                Email: to.email,
                Name: to.name
              }
            ],
            Subject: subject,
            HTMLPart: htmlContent,
            TextPart: textContent
          }
        ]
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${auth}`
        }
      });

      console.log('Email sent successfully:', response.data);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Email sending error:', error.response?.data || error.message);
      return { success: false, error: error.response?.data || error.message };
    }
  }

  async sendOTP(email, name, otp) {
    const subject = 'Email Verification - Padel Chase';
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Welcome to Padel Chase!</h2>
        <p>Hi ${name},</p>
        <p>Thank you for signing up with Padel Chase. To complete your registration, please verify your email address using the OTP below:</p>
        <div style="background-color: #f3f4f6; padding: 20px; text-align: center; margin: 20px 0;">
          <h1 style="color: #1f2937; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
        </div>
        <p>This OTP will expire in 10 minutes.</p>
        <p>If you didn't create an account with us, please ignore this email.</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The Padel Chase Team
        </p>
      </div>
    `;
    
    const textContent = `
      Welcome to Padel Chase!
      
      Hi ${name},
      
      Your email verification OTP is: ${otp}
      
      This OTP will expire in 10 minutes.
      
      Best regards,
      The Padel Chase Team
    `;

    return this.sendEmail({ email, name }, subject, htmlContent, textContent);
  }

  async sendPasswordResetOTP(email, name, otp) {
    const subject = 'Password Reset - Padel Chase';
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">Password Reset Request</h2>
        <p>Hi ${name},</p>
        <p>You requested to reset your password for your Padel Chase account. Use the OTP below to proceed:</p>
        <div style="background-color: #fef2f2; padding: 20px; text-align: center; margin: 20px 0; border: 1px solid #fecaca;">
          <h1 style="color: #dc2626; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
        </div>
        <p>This OTP will expire in 10 minutes.</p>
        <p>If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The Padel Chase Team
        </p>
      </div>
    `;

    return this.sendEmail({ email, name }, subject, htmlContent);
  }

  async sendBookingOTP(email, name, otp, courtName, date, timeSlot) {
    const subject = 'Booking Verification - Padel Chase';
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Verify Your Booking</h2>
        <p>Hi ${name},</p>
        <p>You're about to book <strong>${courtName}</strong> for <strong>${date}</strong> at <strong>${timeSlot}</strong>.</p>
        <p>To confirm your booking, please verify your email address using the OTP below:</p>
        <div style="background-color: #f3f4f6; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
          <h1 style="color: #1f2937; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
        </div>
        <p>This OTP will expire in 5 minutes.</p>
        <p>If you didn't request this booking, please ignore this email.</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The Padel Chase Team
        </p>
      </div>
    `;

    const textContent = `
      Verify Your Booking - Padel Chase

      Hi ${name},

      You're about to book ${courtName} for ${date} at ${timeSlot}.

      Your booking verification OTP is: ${otp}

      This OTP will expire in 5 minutes.

      Best regards,
      The Padel Chase Team
    `;

    return this.sendEmail({ email, name }, subject, htmlContent, textContent);
  }

  async sendBookingConfirmation(email, name, booking) {
    const subject = 'Booking Confirmed - Padel Chase';
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #059669;">Booking Confirmed!</h2>
        <p>Hi ${name},</p>
        <p>Your court booking has been successfully confirmed. Here are the details:</p>

        <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #059669;">
          <h3 style="color: #065f46; margin-top: 0;">Booking Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Court:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.court.name}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Date:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.date}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Time:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.timeSlot.startTime} - ${booking.timeSlot.endTime}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Duration:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.timeSlot.durationMinutes} minutes</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Total Price:</td>
              <td style="padding: 8px 0; color: #1f2937; font-weight: bold;">Rs. ${booking.totalPrice}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Status:</td>
              <td style="padding: 8px 0; color: #059669; font-weight: bold;">${booking.status}</td>
            </tr>
          </table>
        </div>

        <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4 style="color: #92400e; margin-top: 0;">Important Information:</h4>
          <ul style="color: #92400e; margin: 0; padding-left: 20px;">
            <li>Please arrive 10 minutes before your booking time</li>
            <li>Bring your own equipment or rent from our facility</li>
            <li>Cancellations must be made at least 24 hours in advance</li>
            <li>Payment can be made at the venue</li>
          </ul>
        </div>

        <p>We look forward to seeing you on the court!</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The Padel Chase Team<br>
          <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a>
        </p>
      </div>
    `;

    const textContent = `
      Booking Confirmed - Padel Chase

      Hi ${name},

      Your court booking has been successfully confirmed!

      Booking Details:
      Court: ${booking.court.name}
      Date: ${booking.date}
      Time: ${booking.timeSlot.startTime} - ${booking.timeSlot.endTime}
      Duration: ${booking.timeSlot.durationMinutes} minutes
      Total Price: Rs. ${booking.totalPrice}
      Status: ${booking.status}

      Important Information:
      - Please arrive 10 minutes before your booking time
      - Bring your own equipment or rent from our facility
      - Cancellations must be made at least 24 hours in advance
      - Payment can be made at the venue

      We look forward to seeing you on the court!

      Best regards,
      The Padel Chase Team
      <EMAIL>
    `;

    return this.sendEmail({ email, name }, subject, htmlContent, textContent);
  }

  async sendCancellationOTP(email, name, otp, courtName, date, timeSlot) {
    const subject = 'Booking Cancellation Verification - Padel Chase';
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">Verify Booking Cancellation</h2>
        <p>Hi ${name},</p>
        <p>You're about to cancel your booking for <strong>${courtName}</strong> on <strong>${date}</strong> at <strong>${timeSlot}</strong>.</p>
        <p>To confirm the cancellation, please verify using the OTP below:</p>
        <div style="background-color: #fef2f2; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; border: 1px solid #fecaca;">
          <h1 style="color: #dc2626; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
        </div>
        <p>This OTP will expire in 5 minutes.</p>
        <p><strong>Note:</strong> Once cancelled, this booking cannot be recovered.</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The Padel Chase Team
        </p>
      </div>
    `;

    const textContent = `
      Verify Booking Cancellation - Padel Chase

      Hi ${name},

      You're about to cancel your booking for ${courtName} on ${date} at ${timeSlot}.

      Your cancellation verification OTP is: ${otp}

      This OTP will expire in 5 minutes.

      Note: Once cancelled, this booking cannot be recovered.

      Best regards,
      The Padel Chase Team
    `;

    return this.sendEmail({ email, name }, subject, htmlContent, textContent);
  }

  async sendCancellationConfirmation(email, name, booking) {
    const subject = 'Booking Cancelled - Padel Chase';
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">Booking Cancelled</h2>
        <p>Hi ${name},</p>
        <p>Your court booking has been successfully cancelled. Here are the details:</p>

        <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626;">
          <h3 style="color: #991b1b; margin-top: 0;">Cancelled Booking Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Court:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.court.name}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Date:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.date}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Time:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.timeSlot.startTime} - ${booking.timeSlot.endTime}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Amount:</td>
              <td style="padding: 8px 0; color: #1f2937;">Rs. ${booking.totalPrice}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Status:</td>
              <td style="padding: 8px 0; color: #dc2626; font-weight: bold;">Cancelled</td>
            </tr>
          </table>
        </div>

        <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4 style="color: #92400e; margin-top: 0;">Refund Information:</h4>
          <p style="color: #92400e; margin: 0;">
            If you paid in advance, your refund will be processed within 3-5 business days.
            For any questions, please contact our support team.
          </p>
        </div>

        <p>We're sorry to see you cancel your booking. We hope to serve you again soon!</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The Padel Chase Team<br>
          <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a>
        </p>
      </div>
    `;

    const textContent = `
      Booking Cancelled - Padel Chase

      Hi ${name},

      Your court booking has been successfully cancelled.

      Cancelled Booking Details:
      Court: ${booking.court.name}
      Date: ${booking.date}
      Time: ${booking.timeSlot.startTime} - ${booking.timeSlot.endTime}
      Amount: Rs. ${booking.totalPrice}
      Status: Cancelled

      Refund Information:
      If you paid in advance, your refund will be processed within 3-5 business days.
      For any questions, please contact our support team.

      We're sorry to see you cancel your booking. We hope to serve you again soon!

      Best regards,
      The Padel Chase Team
      <EMAIL>
    `;

    return this.sendEmail({ email, name }, subject, htmlContent, textContent);
  }

  async sendBookingReminder(email, name, booking) {
    const subject = 'Booking Reminder - Your Court is Ready! - Padel Chase';
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Your Booking is Coming Up!</h2>
        <p>Hi ${name},</p>
        <p>This is a friendly reminder that your padel court booking is scheduled for <strong>1 hour from now</strong>.</p>

        <div style="background-color: #eff6ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
          <h3 style="color: #1e40af; margin-top: 0;">Booking Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Court:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.court.name}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Date:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.date}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Time:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.timeSlot.startTime} - ${booking.timeSlot.endTime}</td>
            </tr>
            <tr>
              <td style="padding: 8px 0; color: #374151; font-weight: bold;">Duration:</td>
              <td style="padding: 8px 0; color: #1f2937;">${booking.timeSlot.durationMinutes || 60} minutes</td>
            </tr>
          </table>
        </div>

        <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h4 style="color: #92400e; margin-top: 0;">Important Reminders:</h4>
          <ul style="color: #92400e; margin: 0; padding-left: 20px;">
            <li>Please arrive 10 minutes before your booking time</li>
            <li>Bring appropriate sports attire and equipment</li>
            <li>Don't forget to bring water and towels</li>
            <li>Check the weather if playing outdoors</li>
          </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <p style="color: #374151; margin-bottom: 15px;">Need to make changes to your booking?</p>
          <a href="${process.env.FRONTEND_URL}/my-bookings"
             style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
            Manage Booking
          </a>
        </div>

        <p>We're excited to see you on the court! Have a great game!</p>
        <hr style="margin: 30px 0;">
        <p style="color: #6b7280; font-size: 14px;">
          Best regards,<br>
          The Padel Chase Team<br>
          <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a>
        </p>
      </div>
    `;

    const textContent = `
      Your Booking is Coming Up! - Padel Chase

      Hi ${name},

      This is a friendly reminder that your padel court booking is scheduled for 1 hour from now.

      Booking Details:
      Court: ${booking.court.name}
      Date: ${booking.date}
      Time: ${booking.timeSlot.startTime} - ${booking.timeSlot.endTime}
      Duration: ${booking.timeSlot.durationMinutes || 60} minutes

      Important Reminders:
      - Please arrive 10 minutes before your booking time
      - Bring appropriate sports attire and equipment
      - Don't forget to bring water and towels
      - Check the weather if playing outdoors

      Need to make changes? Visit: ${process.env.FRONTEND_URL}/my-bookings

      We're excited to see you on the court! Have a great game!

      Best regards,
      The Padel Chase Team
      <EMAIL>
    `;

    return this.sendEmail({ email, name }, subject, htmlContent, textContent);
  }
}

export default new EmailService();
