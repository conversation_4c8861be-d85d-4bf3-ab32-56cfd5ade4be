import express from 'express';
import {
  trackActivity,
  getUserActivityAnalytics,
  getConversionFunnelAnalytics,
  getAbandonmentAnalytics,
  getPopularityAnalytics
} from '../controllers/analyticsController.js';
import { authenticate, authorize } from '../middleware/auth.js';

const router = express.Router();

// Public route for tracking (can be used by authenticated and anonymous users)
router.post('/track-activity', trackActivity);

// Admin-only analytics routes
router.use(authenticate);
router.use(authorize('admin'));

router.get('/user-activity', getUserActivityAnalytics);
router.get('/conversion-funnel', getConversionFunnelAnalytics);
router.get('/abandonment', getAbandonmentAnalytics);
router.get('/popularity', getPopularityAnalytics);

export default router;
