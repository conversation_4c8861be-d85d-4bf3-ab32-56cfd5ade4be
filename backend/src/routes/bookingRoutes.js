import express from 'express';
import {
  createBooking,
  getUserBookings,
  getAllBookings,
  getBookingById,
  updateBookingStatus,
  cancelBooking,
  sendBookingOTP,
  verifyBookingOTP,
  sendCancellationOTP,
  verifyCancellationOTP
} from '../controllers/bookingController.js';
import { authenticate, authorize } from '../middleware/auth.js';
import {
  validateCreateBooking,
  validateMongoId
} from '../middleware/validation.js';
import { bookingLimiter } from '../middleware/rateLimiter.js';

const router = express.Router();

// All routes require authentication
router.use(authenticate);

// Customer routes
router.post('/send-otp', bookingLimiter, sendBookingOTP);
router.post('/verify-otp', bookingLimiter, verifyBookingOTP);
router.post('/send-cancellation-otp', bookingLimiter, sendCancellationOTP);
router.post('/verify-cancellation-otp', bookingLimiter, verifyCancellationOTP);
router.post('/', bookingLimiter, validateCreateBooking, createBooking);
router.get('/my-bookings', getUserBookings);
router.get('/:id', validateMongoId, getBookingById);
router.patch('/:id/cancel', validateMongoId, cancelBooking);

// Admin routes
router.get('/', authorize('admin', 'staff'), getAllBookings);
router.patch('/:id/status', authorize('admin', 'staff'), validateMongoId, updateBookingStatus);

export default router;
