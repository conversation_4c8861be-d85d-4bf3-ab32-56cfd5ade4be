import express from 'express';
import {
  getOperatingHours,
  getOperatingHoursByDay,
  updateOperatingHours,
  updateMultipleOperatingHours,
  getAvailableSlots
} from '../controllers/operatingHoursController.js';
import { authenticate, authorize, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.get('/', getOperatingHours);
router.get('/:day', getOperatingHoursByDay);
router.get('/:day/slots', getAvailableSlots);

// Protected routes - Admin only
router.use(authenticate);
router.use(authorize('admin', 'staff'));

router.put('/:day', updateOperatingHours);
router.put('/', updateMultipleOperatingHours);

export default router;
