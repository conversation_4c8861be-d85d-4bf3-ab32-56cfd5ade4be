import express from 'express';
import {
  getDashboardStats,
  getRecentBookings,
  getAllBookings,
  updateBookingStatus,
  deleteBooking,
  getAllCourts,
  createCourt,
  updateCourt,
  deleteCourt,
  uploadCourtImage,
  getAllUsers,
  updateUserStatus,
  getBookingAnalytics,
  getRevenueAnalytics,
  getSettings,
  updateSettings
} from '../controllers/adminController.js';
import {
  getOperatingHours,
  updateOperatingHours,
  updateMultipleOperatingHours
} from '../controllers/operatingHoursController.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { upload } from '../middleware/upload.js';

const router = express.Router();

// Apply authentication and admin authorization to all routes
router.use(authenticate);
router.use(authorize('admin'));

// Dashboard routes
router.get('/dashboard/stats', getDashboardStats);
router.get('/bookings/recent', getRecentBookings);

// Booking management routes
router.get('/bookings', getAllBookings);
router.patch('/bookings/:id/status', updateBookingStatus);
router.delete('/bookings/:id', deleteBooking);

// Court management routes
router.get('/courts', getAllCourts);
router.post('/courts', createCourt);
router.patch('/courts/:id', updateCourt);
router.delete('/courts/:id', deleteCourt);
router.post('/courts/:id/upload-image', upload.single('image'), uploadCourtImage);

// Operating hours routes
router.get('/operating-hours', getOperatingHours);
router.patch('/operating-hours/:dayOfWeek', updateOperatingHours);
router.patch('/operating-hours', updateMultipleOperatingHours);

// User management routes
router.get('/users', getAllUsers);
router.patch('/users/:id/status', updateUserStatus);

// Analytics routes
router.get('/analytics/bookings', getBookingAnalytics);
router.get('/analytics/revenue', getRevenueAnalytics);

// Settings routes
router.get('/settings', getSettings);
router.patch('/settings', updateSettings);

export default router;
