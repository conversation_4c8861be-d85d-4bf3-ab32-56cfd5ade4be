import express from 'express';
import {
  getCourts,
  getCourtById,
  createCourt,
  updateCourt,
  deleteCourt,
  getCourtAvailability
} from '../controllers/courtController.js';
import { authenticate, authorize, optionalAuth } from '../middleware/auth.js';
import {
  validateCreateCourt,
  validateUpdateCourt,
  validateCourtQuery,
  validateMongoId
} from '../middleware/validation.js';

const router = express.Router();

// Public routes (with optional authentication for personalized results)
router.get('/', optionalAuth, validateCourtQuery, getCourts);
router.get('/:id', optionalAuth, validateMongoId, getCourtById);
router.get('/:id/availability', optionalAuth, validateMongoId, getCourtAvailability);

// Protected routes - Admin only
router.use(authenticate); // All routes below require authentication
router.use(authorize('admin', 'staff')); // Admin and staff only

router.post('/', validateCreateCourt, createCourt);
router.put('/:id', validateUpdateCourt, updateCourt);
router.delete('/:id', validateMongoId, deleteCourt);

export default router;
