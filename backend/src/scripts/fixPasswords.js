import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import { User } from '../models/index.js';
import connectDB from '../config/database.js';

dotenv.config();

const fixPasswords = async () => {
  try {
    console.log('🔧 Starting password fix...');

    // Connect to database
    await connectDB();

    // Find all users
    const users = await User.find({}).select('+password');
    console.log(`Found ${users.length} users to check`);

    let fixedCount = 0;

    for (const user of users) {
      // Check if password is already hashed (bcrypt hashes start with $2a$, $2b$, or $2y$)
      if (!user.password.startsWith('$2')) {
        console.log(`Fixing password for user: ${user.email}`);
        
        // Hash the plain text password
        const salt = await bcrypt.genSalt(12);
        const hashedPassword = await bcrypt.hash(user.password, salt);
        
        // Update the user directly in the database to avoid triggering middleware again
        await User.updateOne(
          { _id: user._id },
          { password: hashedPassword }
        );
        
        fixedCount++;
      } else {
        console.log(`Password already hashed for user: ${user.email}`);
      }
    }

    console.log(`✅ Fixed ${fixedCount} passwords`);
    console.log('🎉 Password fix completed successfully!');

    process.exit(0);

  } catch (error) {
    console.error('❌ Error fixing passwords:', error);
    process.exit(1);
  }
};

// Run if this file is executed directly
import { fileURLToPath } from 'url';
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  fixPasswords();
}

export default fixPasswords;
