import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { User, Court, Booking, OperatingHours } from '../models/index.js';
import connectDB from '../config/database.js';

dotenv.config();

// Sample data based on your frontend mock data
const sampleUsers = [
  {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'Admin123!',
    phone: '+923001234567',
    role: 'admin',
    isVerified: true
  },
  {
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    password: 'Password123!',
    phone: '+923001234568',
    role: 'customer',
    isVerified: true
  },
  {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    password: 'Password123!',
    phone: '+923001234569',
    role: 'customer',
    isVerified: true
  },
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'Password123!',
    phone: '+923001234570',
    role: 'customer',
    isVerified: true
  }
];

const sampleCourts = [
  {
    name: 'Court 1 - Premium',
    description: 'Professional padel court with premium surface and lighting. Perfect for competitive play.',
    image: null,
    capacity: 4,
    pricePerHour: 45,
    status: 'active'
  },
  {
    name: 'Court 2 - Standard',
    description: 'Standard padel court perfect for casual games and recreational play.',
    image: null,
    capacity: 4,
    pricePerHour: 35,
    status: 'active'
  },
  {
    name: 'Court 3 - Training',
    description: 'Specialized training court with coaching facilities. Ideal for lessons and skill development.',
    image: null,
    capacity: 4,
    pricePerHour: 40,
    status: 'active'
  },
  {
    name: 'Court 4 - VIP',
    description: 'Luxury VIP court with premium amenities and concierge service.',
    image: null,
    capacity: 4,
    pricePerHour: 65,
    status: 'active'
  }
];

const sampleOperatingHours = [
  {
    dayOfWeek: 'monday',
    isOpen: true,
    openTime: '08:00',
    closeTime: '22:00',
    pricingTiers: {
      thirtyMinutes: 50,    // Static pricing for 30 min
      oneHour: 100,         // Per hour pricing for 1+ hours
      twoHours: 180,        // Bulk pricing for 2 hours
      longDuration: 80      // Per hour rate for 2+ hours
    }
  },
  {
    dayOfWeek: 'tuesday',
    isOpen: true,
    openTime: '08:00',
    closeTime: '22:00',
    pricingTiers: {
      thirtyMinutes: 50,
      oneHour: 100,
      twoHours: 180,
      longDuration: 80
    }
  },
  {
    dayOfWeek: 'wednesday',
    isOpen: true,
    openTime: '08:00',
    closeTime: '22:00',
    pricingTiers: {
      thirtyMinutes: 50,
      oneHour: 100,
      twoHours: 180,
      longDuration: 80
    }
  },
  {
    dayOfWeek: 'thursday',
    isOpen: true,
    openTime: '08:00',
    closeTime: '22:00',
    pricingTiers: {
      thirtyMinutes: 50,
      oneHour: 100,
      twoHours: 180,
      longDuration: 80
    }
  },
  {
    dayOfWeek: 'friday',
    isOpen: true,
    openTime: '08:00',
    closeTime: '23:00',
    pricingTiers: {
      thirtyMinutes: 60,    // Weekend premium
      oneHour: 120,
      twoHours: 220,
      longDuration: 100
    }
  },
  {
    dayOfWeek: 'saturday',
    isOpen: true,
    openTime: '09:00',
    closeTime: '23:00',
    pricingTiers: {
      thirtyMinutes: 60,    // Weekend premium
      oneHour: 120,
      twoHours: 220,
      longDuration: 100
    }
  },
  {
    dayOfWeek: 'sunday',
    isOpen: true,
    openTime: '09:00',
    closeTime: '20:00',
    pricingTiers: {
      thirtyMinutes: 55,    // Sunday pricing
      oneHour: 110,
      twoHours: 200,
      longDuration: 90
    }
  }
];



const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Connect to database
    await connectDB();

    // Clear existing data
    console.log('🗑️  Clearing existing data...');
    await User.deleteMany({});
    await Court.deleteMany({});
    await Booking.deleteMany({});
    await OperatingHours.deleteMany({});

    // Seed users (create individually to trigger password hashing middleware)
    console.log('👥 Seeding users...');
    const users = [];
    for (const userData of sampleUsers) {
      const user = new User(userData);
      await user.save(); // This triggers the pre-save middleware for password hashing
      users.push(user);
    }
    console.log(`✅ Created ${users.length} users`);

    // Seed courts
    console.log('🏟️  Seeding courts...');
    const courts = await Court.insertMany(sampleCourts);
    console.log(`✅ Created ${courts.length} courts`);

    // Seed operating hours
    console.log('⏰ Seeding operating hours...');
    const operatingHours = await OperatingHours.insertMany(sampleOperatingHours);
    console.log(`✅ Created ${operatingHours.length} operating hour records`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   Users: ${users.length}`);
    console.log(`   Courts: ${courts.length}`);
    console.log(`   Operating Hours: ${operatingHours.length}`);
    console.log('\n🔐 Admin credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Admin123!');

    process.exit(0);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
import { fileURLToPath } from 'url';
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  seedDatabase();
}

export default seedDatabase;
