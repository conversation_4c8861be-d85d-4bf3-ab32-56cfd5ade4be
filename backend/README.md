# Padel Chase Backend API

A comprehensive Node.js backend API for the Padel Chase booking application, built with Express.js and MongoDB.

## Features

- 🔐 **Authentication & Authorization**: JWT-based auth with role management
- 🏟️ **Court Management**: CRUD operations for padel courts
- 📅 **Booking System**: Complete booking lifecycle management
- ⏰ **Operating Hours**: Flexible scheduling system
- 🎉 **Holiday Management**: Special day handling
- 📁 **File Upload**: Image upload for courts
- 🛡️ **Security**: Rate limiting, CORS, helmet protection
- 📊 **Validation**: Comprehensive input validation
- 🚀 **Performance**: Optimized database queries and indexing

## Tech Stack

- **Runtime**: Node.js with ES6 modules
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Express Validator
- **Security**: Helmet, CORS, Rate Limiting
- **File Upload**: Multer
- **Logging**: Morgan

## Prerequisites

- Node.js (v16 or higher)
- MongoDB (v4.4 or higher)
- npm or yarn

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd padel-chase/backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   PORT=5000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/padel-chase
   JWT_SECRET=your-super-secret-jwt-key
   JWT_EXPIRE=7d
   CORS_ORIGIN=http://localhost:3000
   ```

4. **Start MongoDB**
   Make sure MongoDB is running on your system.

5. **Seed the database** (optional)
   ```bash
   npm run seed
   ```

6. **Start the server**
   ```bash
   # Development mode with auto-reload
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh-token` - Refresh access token
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/profile` - Get user profile
- `PUT /api/v1/auth/profile` - Update user profile

### Courts
- `GET /api/v1/courts` - Get all courts (with filtering)
- `GET /api/v1/courts/:id` - Get court by ID
- `GET /api/v1/courts/:id/availability` - Get court availability
- `POST /api/v1/courts` - Create court (admin only)
- `PUT /api/v1/courts/:id` - Update court (admin only)
- `DELETE /api/v1/courts/:id` - Delete court (admin only)

### Bookings
- `POST /api/v1/bookings` - Create booking
- `GET /api/v1/bookings/my-bookings` - Get user's bookings
- `GET /api/v1/bookings` - Get all bookings (admin only)
- `GET /api/v1/bookings/:id` - Get booking by ID
- `PATCH /api/v1/bookings/:id/cancel` - Cancel booking
- `PATCH /api/v1/bookings/:id/status` - Update booking status (admin only)

### Operating Hours
- `GET /api/v1/operating-hours` - Get all operating hours
- `GET /api/v1/operating-hours/:day` - Get hours for specific day
- `GET /api/v1/operating-hours/:day/slots` - Get available slots
- `PUT /api/v1/operating-hours/:day` - Update hours for day (admin only)
- `PUT /api/v1/operating-hours` - Update multiple days (admin only)

### Holidays
- `GET /api/v1/holidays` - Get all holidays
- `GET /api/v1/holidays/check/:date` - Check if date is holiday
- `GET /api/v1/holidays/range` - Get holidays in date range
- `POST /api/v1/holidays` - Create holiday (admin only)
- `PUT /api/v1/holidays/:id` - Update holiday (admin only)
- `DELETE /api/v1/holidays/:id` - Delete holiday (admin only)

## Database Models

### User
- Personal information (name, email, phone)
- Authentication (password, role)
- Preferences and settings

### Court
- Court details (name, description, location)
- Pricing and capacity
- Amenities and specifications
- Maintenance scheduling

### Booking
- Court and customer references
- Date and time slot
- Status and payment information
- Special requests and notes

### Operating Hours
- Day-specific hours
- Break periods
- Special notes

### Holiday
- Holiday information
- Custom hours or full closure
- Recurring holiday support

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Prevents abuse and DoS attacks
- **Input Validation**: Comprehensive request validation
- **CORS Protection**: Configurable cross-origin requests
- **Helmet Security**: Security headers and protection
- **Password Hashing**: Bcrypt password encryption

## Error Handling

The API includes comprehensive error handling:
- Validation errors with detailed messages
- Authentication and authorization errors
- Database operation errors
- File upload errors
- Rate limiting errors

## Development

### Scripts
- `npm run dev` - Start development server with auto-reload
- `npm start` - Start production server
- `npm run seed` - Seed database with sample data

### Code Structure
```
src/
├── config/          # Configuration files
├── controllers/     # Route controllers
├── middleware/      # Custom middleware
├── models/          # Database models
├── routes/          # API routes
├── scripts/         # Utility scripts
├── utils/           # Helper utilities
└── server.js        # Main server file
```

## Deployment

1. **Environment Variables**: Set production environment variables
2. **Database**: Ensure MongoDB is accessible
3. **Process Manager**: Use PM2 or similar for production
4. **Reverse Proxy**: Configure nginx or similar
5. **SSL**: Enable HTTPS in production

## Default Admin Account

After seeding the database:
- **Email**: <EMAIL>
- **Password**: Admin123!

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the ISC License.
