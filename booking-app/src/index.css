@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
  font-family: 'Inter', system-ui, sans-serif;
}

/* iOS Safe Area Support */
:root {
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
}

/* Remove safe area from root - let individual components handle it */
/* 
@supports (padding: max(0px)) {
  #root {
    padding-top: max(var(--safe-area-inset-top), 0px);
    padding-bottom: max(var(--safe-area-inset-bottom), 0px);
    padding-left: max(var(--safe-area-inset-left), 0px);
    padding-right: max(var(--safe-area-inset-right), 0px);
  }
}
*/

/* For fixed headers */
.header-fixed {
  padding-top: calc(1rem + var(--safe-area-inset-top));
}

/* Mobile menu safe area */
.mobile-menu-safe {
  padding-top: var(--safe-area-inset-top);
  padding-bottom: var(--safe-area-inset-bottom);
}
