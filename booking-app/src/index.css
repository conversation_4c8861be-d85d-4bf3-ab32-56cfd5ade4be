@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Color Theme Variables */
:root {
  /* Primary Colors */
  --color-primary-50: #FEF3F0;
  --color-primary-100: #FDE6E0;
  --color-primary-200: #FBCDC1;
  --color-primary-300: #F8B4A2;
  --color-primary-400: #F59B83;
  --color-primary-500: #F28264;
  --color-primary-600: #ED5E12;
  --color-primary-700: #D4520F;
  --color-primary-800: #BB460D;
  --color-primary-900: #A23A0B;
  --color-primary-950: #892E09;

  /* Secondary Colors */
  --color-secondary-50: #FEFCF0;
  --color-secondary-100: #FDF8E0;
  --color-secondary-200: #FBF1C1;
  --color-secondary-300: #F8EAA2;
  --color-secondary-400: #F5E383;
  --color-secondary-500: #F2DC64;
  --color-secondary-600: #EDB112;
  --color-secondary-700: #D49E0F;
  --color-secondary-800: #BB8B0D;
  --color-secondary-900: #A2780B;
  --color-secondary-950: #896509;

  /* Dark Colors (replaces black) */
  --color-dark-50: #F7F7F7;
  --color-dark-100: #EFEFEF;
  --color-dark-200: #DFDFDF;
  --color-dark-300: #CFCFCF;
  --color-dark-400: #BFBFBF;
  --color-dark-500: #AFAFAF;
  --color-dark-600: #9F9F9F;
  --color-dark-700: #8F8F8F;
  --color-dark-800: #7F7F7F;
  --color-dark-900: #2C2C2C;
  --color-dark-950: #1A1A1A;

  /* Light Colors (replaces white) */
  --color-light-50: #FFFFFF;
  --color-light-100: #FAFAFA;
  --color-light-200: #F5F5F5;
  --color-light-300: #F0F0F0;
  --color-light-400: #EBEBEB;
  --color-light-500: #E6E6E6;
  --color-light-600: #E1E1E1;
  --color-light-700: #DCDCDC;
  --color-light-800: #D7D7D7;
  --color-light-900: #D2D2D2;
  --color-light-950: #CDCDCD;

  /* iOS Safe Area Support */
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  background-color: var(--color-light-100);
  color: var(--color-dark-900);
}

/* Remove safe area from root - let individual components handle it */
/*
@supports (padding: max(0px)) {
  #root {
    padding-top: max(var(--safe-area-inset-top), 0px);
    padding-bottom: max(var(--safe-area-inset-bottom), 0px);
    padding-left: max(var(--safe-area-inset-left), 0px);
    padding-right: max(var(--safe-area-inset-right), 0px);
  }
}
*/

/* For fixed headers */
.header-fixed {
  padding-top: calc(1rem + var(--safe-area-inset-top));
}

/* Mobile menu safe area */
.mobile-menu-safe {
  padding-top: var(--safe-area-inset-top);
  padding-bottom: var(--safe-area-inset-bottom);
}

/* Custom component styles using the new color theme */
@layer components {
  .btn-primary {
    @apply bg-primary-600 text-light-50 hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-dark-900 hover:bg-secondary-700 active:bg-secondary-800;
  }

  .btn-outline {
    @apply border border-dark-300 bg-light-50 text-dark-900 hover:bg-light-200 active:bg-light-300;
  }

  .btn-ghost {
    @apply text-dark-700 hover:bg-light-200 active:bg-light-300;
  }

  .card {
    @apply bg-light-50 border border-light-300 shadow-sm;
  }

  .input {
    @apply border border-dark-300 bg-light-50 text-dark-900 placeholder:text-dark-500 focus:border-primary-600 focus:ring-primary-600;
  }

  .text-primary {
    @apply text-primary-600;
  }

  .text-secondary {
    @apply text-secondary-600;
  }

  .text-dark {
    @apply text-dark-900;
  }

  .text-light {
    @apply text-light-50;
  }

  .bg-primary {
    @apply bg-primary-600;
  }

  .bg-secondary {
    @apply bg-secondary-600;
  }

  .bg-dark {
    @apply bg-dark-900;
  }

  .bg-light {
    @apply bg-light-100;
  }
}
