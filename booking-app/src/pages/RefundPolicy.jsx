import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

const RefundPolicy = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Refund Policy</h1>
          <p className="text-gray-600 mt-2">Last updated: {new Date().toLocaleDateString()}</p>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm p-8 space-y-8">
          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. Cancellation Policy</h2>
            <div className="space-y-4 text-gray-700">
              <p><strong>Free Cancellation:</strong> Bookings can be cancelled free of charge up to 2 hours before the scheduled time.</p>
              <p><strong>Late Cancellation:</strong> Cancellations made less than 2 hours before the booking time will incur a 50% charge.</p>
              <p><strong>No-Show:</strong> Failure to show up for your booking will result in full charge with no refund.</p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. Refund Eligibility</h2>
            <div className="space-y-4 text-gray-700">
              <h3 className="text-lg font-medium">Full Refund Scenarios:</h3>
              <p>• Court unavailability due to maintenance or technical issues</p>
              <p>• Facility closure due to weather conditions or emergencies</p>
              <p>• System errors resulting in double bookings</p>
              <p>• Cancellation by facility management</p>
              
              <h3 className="text-lg font-medium mt-6">Partial Refund Scenarios:</h3>
              <p>• Late cancellation (more than 2 hours but less than 24 hours): 50% refund</p>
              <p>• Equipment failure during play: Prorated refund based on unused time</p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. Refund Process</h2>
            <div className="space-y-4 text-gray-700">
              <p><strong>Processing Time:</strong> Refunds are processed within 5-7 business days from approval.</p>
              <p><strong>Refund Method:</strong> Refunds will be credited back to the original payment method.</p>
              <p><strong>Cash Payments:</strong> For cash payments made at venue, refunds will be processed as bank transfers or cash at the facility.</p>
              <p><strong>Notification:</strong> You will receive an email confirmation once the refund is processed.</p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. How to Request a Refund</h2>
            <div className="space-y-4 text-gray-700">
              <p>1. <strong>Through the App:</strong> Use the cancellation feature in your booking details</p>
              <p>2. <strong>Contact Support:</strong> Email <NAME_EMAIL> with your booking details</p>
              <p>3. <strong>Phone:</strong> Call our support line at +92-XXX-XXXXXXX</p>
              <p>4. <strong>In Person:</strong> Visit the facility and speak with our staff</p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. Non-Refundable Situations</h2>
            <div className="space-y-4 text-gray-700">
              <p>• No-show without prior cancellation</p>
              <p>• Violation of facility rules resulting in booking termination</p>
              <p>• Personal emergencies or changes in plans</p>
              <p>• Weather conditions that do not affect indoor courts</p>
              <p>• Dissatisfaction with court quality (unless there are genuine facility issues)</p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. Special Circumstances</h2>
            <div className="space-y-4 text-gray-700">
              <p><strong>Medical Emergencies:</strong> Refunds may be considered on a case-by-case basis with proper documentation.</p>
              <p><strong>Force Majeure:</strong> In cases of natural disasters, government restrictions, or other unforeseeable circumstances, full refunds will be provided.</p>
              <p><strong>Recurring Bookings:</strong> For recurring bookings, each session is treated individually for refund purposes.</p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">7. Dispute Resolution</h2>
            <p className="text-gray-700 leading-relaxed">
              If you disagree with a refund decision, you may appeal by contacting our customer service team within 
              30 days of the original decision. We will review your case and provide a final decision within 10 business days.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">8. Contact Information</h2>
            <div className="text-gray-700 space-y-2">
              <p>For refund requests or questions about this policy:</p>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Phone:</strong> +92-XXX-XXXXXXX</p>
              <p><strong>Support Hours:</strong> Monday to Friday, 9:00 AM - 6:00 PM</p>
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">9. Policy Updates</h2>
            <p className="text-gray-700 leading-relaxed">
              This refund policy may be updated from time to time. Users will be notified of any significant changes 
              via email or app notifications. The updated policy will apply to all bookings made after the effective date.
            </p>
          </section>
        </div>
      </div>
    </div>
  );
};

export default RefundPolicy;
