import { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Calendar,
  Clock,
  Users,
  Star,
  MapPin,
  Shield,
  ArrowRight
} from 'lucide-react';
import { Button } from '../components/ui';
import AuthModal from '../components/auth/AuthModal';
import { useAuth } from '../hooks/useAuth';

const LandingPage = () => {
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState('login');
  const { isAuthenticated, user } = useAuth();

  const openAuthModal = (mode) => {
    setAuthMode(mode);
    setAuthModalOpen(true);
  };

  const features = [
    {
      icon: Calendar,
      title: "Easy Booking",
      description: "Book your favorite court in just a few clicks"
    },
    {
      icon: Clock,
      title: "Flexible Timing",
      description: "Choose from 30 minutes to 8 hours duration"
    },
    {
      icon: Users,
      title: "Community",
      description: "Connect with fellow padel enthusiasts"
    },
    {
      icon: Shield,
      title: "Reliable Service",
      description: "Consistent quality and professional service"
    }
  ];



  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section  style={{ backgroundImage: `url(/icons/bg-image.jpg)` }}  className=" bg-no-repeat bg-cover relative text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-7xl mx-auto px-4 py-20 md:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                Pakistan's Premier
                <span className="block text-yellow-400">Padel Booking</span>
                Platform
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100">
                Book premium padel courts instantly. Play with friends. Build your community.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                {!isAuthenticated ? (
                  <>
                    <Button
                      size="lg"
                      className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold px-8 py-4 text-lg"
                      onClick={() => openAuthModal('register')}
                    >
                      Start Playing Today
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                      className="border-white text-blue hover:bg-white hover:text-blue-600 px-8 py-4 text-lg bg-transparent"
                      onClick={() => openAuthModal('login')}
                    >
                      Login
                    </Button>
                  </>
                ) : (
                  <>
                    <Link to="/booking">
                      <Button
                        size="lg"
                        className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold px-8 py-4 text-lg"
                      >
                        Book a Court Now
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Button>
                    </Link>
                    <Link to="/my-bookings">
                      <Button
                        variant="outline"
                        size="lg"
                        className="border-white text-black hover:bg-white hover:text-blue-600 px-8 py-4 text-lg bg-transparent"
                      >
                        My Bookings
                      </Button>
                    </Link>
                  </>
                )}
              </div>
              <div className="mt-8 flex items-center justify-center lg:justify-start space-x-6 text-blue-100">
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  <span>Premium Courts</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  <span>Easy Booking</span>
                </div>
                <div className="flex items-center">
                  <Star className="h-5 w-5 mr-2" />
                  <span>Quality Service</span>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold mb-2">Why Choose Us?</h3>
                  <p className="text-blue-100">Experience the best padel booking platform</p>
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-400">🏆</div>
                    <div className="text-sm text-blue-100">Premium Courts</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-400">⚡</div>
                    <div className="text-sm text-blue-100">Instant Booking</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Padel Chase?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the best padel booking platform in Pakistan with premium features designed for players.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600">
              Get started in just 3 simple steps
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Sign Up</h3>
              <p className="text-gray-600">Create your account in seconds with email verification</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Choose Court</h3>
              <p className="text-gray-600">Select your preferred court, date, and time duration</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Play & Enjoy</h3>
              <p className="text-gray-600">Show up and enjoy your game on premium courts</p>
            </div>
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          {!isAuthenticated ? (
            <>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Ready to Start Playing?
              </h2>
              <p className="text-xl text-blue-100 mb-8">
                Join Pakistan's largest padel community today and book your first court!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold px-8 py-4 text-lg"
                  onClick={() => openAuthModal('register')}
                >
                  Get Started Free
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white text-blue hover:bg-white hover:text-blue-600 px-8 py-4 text-lg bg-transparent"
                  onClick={() => openAuthModal('login')}
                >
                  I Have an Account
                </Button>
              </div>
            </>
          ) : (
            <>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Welcome back, {user?.firstName}!
              </h2>
              <p className="text-xl text-blue-100 mb-8">
                Ready to book your next game? Check your upcoming bookings or reserve a new court.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/booking">
                  <Button
                    size="lg"
                    className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold px-8 py-4 text-lg"
                  >
                    Book New Court
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link to="/my-bookings">
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white text-black hover:bg-white hover:text-blue-600 px-8 py-4 text-lg bg-transparent"
                  >
                    View My Bookings
                  </Button>
                </Link>
              </div>
            </>
          )}
        </div>
      </section>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        defaultMode={authMode}
      />
    </div>
  );
};

export default LandingPage;
