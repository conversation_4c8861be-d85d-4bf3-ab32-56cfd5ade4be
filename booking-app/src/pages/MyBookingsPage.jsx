import { useState, useEffect } from 'react';
import { format, isAfter, isBefore, isToday } from 'date-fns';
import { Calendar, Clock, MapPin, User, Phone, Mail, MoreVertical, Download, Edit, Trash2 } from 'lucide-react';
import { Card, Button, Badge, Modal, Table } from '../components/ui';
import { useApp } from '../context/AppContext';
import { useAuth } from '../hooks/useAuth';
import { bookingService } from '../services';
import AuthModal from '../components/auth/AuthModal';
import CancellationOTP from '../components/booking/CancellationOTP';
import CancellationRestrictionModal from '../components/ui/CancellationRestrictionModal';

// Utility function to parse date strings as local dates
const parseLocalDate = (dateString) => {
  if (dateString.includes('T')) {
    // If it's an ISO string, parse it normally
    return new Date(dateString);
  }
  // If it's a date string like "2024-09-04", parse as local date
  const [year, month, day] = dateString.split('-').map(Number);
  return new Date(year, month - 1, day); // month is 0-indexed
};

const MyBookingsPage = () => {
  const { state, actions } = useApp();
  const { isAuthenticated } = useAuth();

  const [filter, setFilter] = useState('upcoming');
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [loading, setLoading] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 10;
  const [showCancellationOTP, setShowCancellationOTP] = useState(false);
  const [cancellationBooking, setCancellationBooking] = useState(null);
  const [cancellationLoading, setCancellationLoading] = useState(false);
  const [showRestrictionModal, setShowRestrictionModal] = useState(false);
  const [restrictionHours, setRestrictionHours] = useState(0);

  // Load user bookings on component mount
  useEffect(() => {
    if (isAuthenticated) {
      actions.fetchUserBookings();
    }
  }, [isAuthenticated]);

  const getBookingStatus = (booking) => {
    // Only use booking.status from backend
    // Admin controls when bookings are marked as completed/cancelled
    if (booking.status === 'cancelled') return 'cancelled';
    if (booking.status === 'completed') return 'completed';

    // All pending/confirmed bookings are upcoming regardless of date/time
    return 'upcoming';
  };

  const getStatusConfig = (status) => {
    const configs = {
      upcoming: { variant: 'primary', text: 'Upcoming' },
      completed: { variant: 'success', text: 'Completed' },
      cancelled: { variant: 'error', text: 'Cancelled' }
    };
    return configs[status] || configs.upcoming;
  };

  const allFilteredBookings = state.bookings.filter(booking => {
    if (filter === 'all') return true;
    if (filter === 'past') {
      const status = getBookingStatus(booking);
      return status === 'completed' || status === 'cancelled';
    }
    return getBookingStatus(booking) === filter;
  });

  // Apply pagination only for "All" tab
  const filteredBookings = filter === 'all'
    ? allFilteredBookings.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
    : allFilteredBookings;

  // Update total pages when filter or bookings change
  useEffect(() => {
    if (filter === 'all') {
      setTotalPages(Math.ceil(allFilteredBookings.length / itemsPerPage));
    } else {
      setTotalPages(1);
      setCurrentPage(1);
    }
  }, [filter, state.bookings, allFilteredBookings.length]);

  const handleViewDetails = (booking) => {
    setSelectedBooking(booking);
    setShowDetails(true);
  };

  const handleCancelBooking = async (booking) => {
    // Check if booking can be cancelled (at least 2 hours before)
    const now = new Date();
    const islamabadTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Karachi"}));

    // Parse booking date and time
    const bookingDate = parseLocalDate(booking.date);
    const [hours, minutes] = booking.timeSlot.startTime.split(':').map(Number);
    const bookingDateTime = new Date(bookingDate);
    bookingDateTime.setHours(hours, minutes, 0, 0);

    const hoursUntilBooking = (bookingDateTime - islamabadTime) / (1000 * 60 * 60);

    if (hoursUntilBooking < 2) {
      setRestrictionHours(hoursUntilBooking);
      setShowRestrictionModal(true);
      return;
    }

    setCancellationBooking(booking);
    setCancellationLoading(true);

    try {
      const result = await bookingService.sendCancellationOTP(booking._id);
      if (result.success) {
        setShowCancellationOTP(true);
      } else {
        alert(result.error?.message || 'Failed to send cancellation OTP');
      }
    } catch (error) {
      console.error('Send cancellation OTP error:', error);
      alert('Failed to send cancellation OTP. Please try again.');
    } finally {
      setCancellationLoading(false);
    }
  };

  const handleCancellationOTPVerify = async (otp) => {
    if (!cancellationBooking) return;

    try {
      const result = await bookingService.verifyCancellationOTP({
        otp,
        bookingId: cancellationBooking._id
      });

      if (result.booking) {
        // Update the booking in state
        actions.updateBooking(result.booking);
        // Refresh bookings
        await actions.fetchUserBookings();

        setShowCancellationOTP(false);
        setCancellationBooking(null);
        alert('Booking cancelled successfully!');
      } else {
        alert(result.error?.message || 'Failed to cancel booking');
      }
    } catch (error) {
      console.error('Cancel booking error:', error);
      alert('Failed to cancel booking. Please try again.');
    }
  };

  const handleDownloadReceipt = (bookingId) => {
    // In a real app, this would download the receipt
    console.log('Download receipt:', bookingId);
  };

  // Show login message if not authenticated
  if (!isAuthenticated) {
    return (
      <>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="mb-6">
              <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Please Login</h1>
              <p className="text-gray-600">You need to be logged in to view your bookings.</p>
            </div>
            <div className="space-y-3">
              <Button
                onClick={() => setAuthModalOpen(true)}
                className="w-full"
              >
                Login to View Bookings
              </Button>
              <Button
                variant="outline"
                onClick={() => window.history.back()}
                className="w-full"
              >
                Go Back
              </Button>
            </div>
          </div>
        </div>

        <AuthModal
          isOpen={authModalOpen}
          onClose={() => setAuthModalOpen(false)}
          defaultMode="login"
        />
      </>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-4 md:py-8">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">My Bookings</h1>
          <p className="text-gray-600 text-sm md:text-base">Manage your court reservations</p>
        </div>

      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2 md:space-x-1 md:gap-0 bg-gray-100 p-1 rounded-lg w-full md:w-fit">
          {[
            { key: 'upcoming', label: 'Upcoming' },
            { key: 'past', label: 'Past' },
            { key: 'all', label: 'All' }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key)}
              className={`flex-1 md:flex-none px-3 md:px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                filter === tab.key
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Bookings List */}
      {filteredBookings.length === 0 ? (
        <Card>
          <div className="p-12 text-center">
            <Calendar className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-secondary-900 mb-2">No bookings found</h3>
            <p className="text-secondary-600 mb-4">
              {filter === 'all'
                ? "You haven't made any bookings yet."
                : `No ${filter} bookings found.`}
            </p>
            <Button onClick={() => window.location.href = '/'}>
              Book a Court
            </Button>
          </div>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredBookings.map(booking => {
            const status = getBookingStatus(booking);
            const statusConfig = getStatusConfig(status);

            return (
              <Card key={booking.id} className="hover:shadow-lg transition-shadow">
                <div className="p-4 md:p-6">
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3 space-y-2 sm:space-y-0 mb-3">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {booking.court.name}
                          </h3>
                          <Badge variant={statusConfig.variant}>
                            {statusConfig.text}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* Details */}
                    <div className="space-y-3">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 flex-shrink-0" />
                          <span className="truncate">{format(parseLocalDate(booking.date), 'MMM d, yyyy')}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 flex-shrink-0" />
                          <span>{booking.timeSlot.startTime} - {booking.timeSlot.endTime}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">Rs. {booking.totalPrice}</span>
                        </div>
                      </div>


                    </div>

                    {/* Actions */}
                    <div className="flex flex-col sm:flex-row gap-2 pt-2 border-t border-gray-100">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(booking)}
                        className="flex-1 sm:flex-none"
                      >
                        View Details
                      </Button>
                      {status === 'upcoming' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCancelBooking(booking)}
                          className="flex-1 sm:flex-none text-red-600 hover:text-red-700 hover:border-red-300"
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      )}

      {/* Pagination for All tab */}
      {filter === 'all' && totalPages > 1 && (
        <div className="mt-6 flex justify-center">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                  className="w-10"
                >
                  {page}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Booking Details Modal */}
      <Modal
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
        title="Booking Details"
        size="lg"
      >
        {selectedBooking && (
          <div className="space-y-6">
            {/* Booking Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Court Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.court.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-secondary-600">{selectedBooking.court.location}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Date & Time</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-secondary-500" />
                      <span>{format(parseLocalDate(selectedBooking.date), 'EEEE, MMMM d, yyyy')}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.timeSlot.startTime} - {selectedBooking.timeSlot.endTime}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Contact Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.customer?.firstName || state.user?.firstName} {selectedBooking.customer?.lastName || state.user?.lastName}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.customer?.email || state.user?.email}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.customer?.phone || state.user?.phone}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Booking Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-secondary-600">Total Price:</span>
                      <span className="font-medium">Rs. {selectedBooking.totalPrice}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-secondary-600">Status:</span>
                      <Badge variant={getStatusConfig(getBookingStatus(selectedBooking)).variant}>
                        {getStatusConfig(getBookingStatus(selectedBooking)).text}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            {getBookingStatus(selectedBooking) === 'upcoming' && (
              <div className="flex space-x-3 pt-4 border-t border-secondary-200">
                <Button
                  variant="outline"
                  onClick={() => handleCancelBooking(selectedBooking)}
                  className="flex-1 text-error-600 hover:text-error-700 hover:border-error-300"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Cancel Booking
                </Button>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Cancellation OTP Modal */}
      <CancellationOTP
        isOpen={showCancellationOTP}
        onClose={() => {
          setShowCancellationOTP(false);
          setCancellationBooking(null);
        }}
        onVerify={handleCancellationOTPVerify}
        email={state.user?.email}
        bookingDetails={cancellationBooking ? {
          court: { name: cancellationBooking.court?.name },
          date: format(parseLocalDate(cancellationBooking.date), 'EEEE, MMMM d, yyyy'),
          timeSlot: {
            startTime: cancellationBooking.timeSlot?.startTime,
            endTime: cancellationBooking.timeSlot?.endTime
          },
          totalPrice: cancellationBooking.totalPrice
        } : null}
        loading={cancellationLoading}
      />

      {/* Cancellation Restriction Modal */}
      <CancellationRestrictionModal
        isOpen={showRestrictionModal}
        onClose={() => setShowRestrictionModal(false)}
        hoursRemaining={restrictionHours}
      />
      </div>
    </div>
  );
};

export default MyBookingsPage;
