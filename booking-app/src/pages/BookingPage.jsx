import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { ArrowR<PERSON>, ArrowLeft, CheckCircle } from 'lucide-react';
import { But<PERSON>, Modal } from '../components/ui';
import { CourtGrid } from '../components/courts';
import { DatePicker, TimeSlotGrid, BookingForm, BookingConfirmation } from '../components/booking';
import OTPVerification from '../components/booking/OTPVerification';
import { bookingService } from '../services/bookingService';
import useActivityTracker from '../hooks/useActivityTracker';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import DurationSelector from '../components/booking/DurationSelector';
import { useApp } from '../context/AppContext';
import { courtService } from '../services';
import { useNavigate } from 'react-router-dom';


const BookingPage = () => {
  const { state, actions } = useApp();
  const { isAuthenticated } = state;
  const navigate = useNavigate();
  const {
    trackBookingFlow,
    trackAbandonment,
    trackFormInteraction,
    trackError
  } = useActivityTracker();

  const [currentStep, setCurrentStep] = useState(1);
  const [selectedCourt, setSelectedCourt] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [selectedDuration, setSelectedDuration] = useState(60); // in minutes
  const [timeSlots, setTimeSlots] = useState([]);
  const [filteredCourts, setFilteredCourts] = useState([]);
  const [booking, setBooking] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [loading, setLoading] = useState(false);
  const [availabilityLoading, setAvailabilityLoading] = useState(false);
  const [bookingError, setBookingError] = useState('');
  const [showOTPVerification, setShowOTPVerification] = useState(false);
  const [pendingBookingData, setPendingBookingData] = useState(null);

  const steps = [
    { number: 1, title: 'Select Court', description: 'Choose your preferred court' },
    { number: 2, title: 'Pick Date & Time', description: 'Select date and time slot' },
    { number: 3, title: 'Book & Pay', description: 'Complete your booking' }
  ];

  // Initialize courts from context
  useEffect(() => {
    setFilteredCourts(state.courts);
  }, [state.courts]);

  // Load time slots when court, date, or duration are selected
  useEffect(() => {
    if (selectedCourt && selectedDate) {
      loadTimeSlots();
    }
  }, [selectedCourt, selectedDate, selectedDuration]);

  const loadTimeSlots = async () => {
    if (!selectedCourt || !selectedDate) return;

    setAvailabilityLoading(true);
    try {
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      const { availability, error } = await courtService.getCourtAvailability(selectedCourt._id, dateString, selectedDuration);

      if (error) {
        console.error('Error loading availability:', error);
        setTimeSlots([]);
      } else {
        setTimeSlots(availability.timeSlots || []);
      }
    } catch (error) {
      console.error('Error loading time slots:', error);
      setTimeSlots([]);
    } finally {
      setAvailabilityLoading(false);
    }
  };

  const handleCourtSelect = (court) => {
    setSelectedCourt(court);
    setCurrentStep(2);
    setSelectedSlot(null);

    // Track court selection
    trackBookingFlow('court_selection', {
      courtId: court._id,
      courtName: court.name,
      step: 1
    });

    // Auto-select today's date
    if (!selectedDate) {
      setSelectedDate(new Date());
    }
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);
    setSelectedSlot(null);

    // Track date selection
    trackBookingFlow('date_selection', {
      date: format(date, 'yyyy-MM-dd'),
      courtId: selectedCourt._id,
      step: 2
    });
  };

  const handleSlotSelect = (slot) => {
    setSelectedSlot(slot);
    setCurrentStep(3);

    // Track time slot selection
    trackBookingFlow('time_slot_selection', {
      timeSlot: `${slot.startTime} - ${slot.endTime}`,
      price: slot.price,
      duration: selectedDuration,
      courtId: selectedCourt._id,
      step: 3
    });
  };

  const handleBookingSubmit = async (formData) => {
    setBookingError(''); // Clear previous errors

    // Track booking form submission
    trackBookingFlow('booking_form', {
      courtId: selectedCourt._id,
      date: format(selectedDate, 'yyyy-MM-dd'),
      timeSlot: `${selectedSlot.startTime} - ${selectedSlot.endTime}`,
      step: 4
    });

    if (!isAuthenticated) {
      setBookingError('Please login to make a booking');
      trackAbandonment('not_authenticated', {
        step: 'booking_form',
        courtId: selectedCourt._id
      });
      return;
    }

    setLoading(true);

    try {
      // Send OTP first
      const otpPayload = {
        courtId: selectedCourt._id,
        date: format(selectedDate, 'yyyy-MM-dd'),
        timeSlot: {
          startTime: selectedSlot.startTime,
          endTime: selectedSlot.endTime
        }
      };

      const otpResult = await bookingService.sendBookingOTP(otpPayload);

      if (otpResult.success) {
        // Store booking data and show OTP verification
        const bookingPayload = {
          courtId: selectedCourt._id,
          date: format(selectedDate, 'yyyy-MM-dd'),
          timeSlot: {
            startTime: selectedSlot.startTime,
            endTime: selectedSlot.endTime
          },
          durationMinutes: selectedDuration,
          formData
        };

        setPendingBookingData(bookingPayload);
        setShowOTPVerification(true);
      } else {
        setBookingError(otpResult.error?.message || 'Failed to send verification email');
        trackError(new Error(otpResult.error?.message || 'Failed to send verification email'), {
          step: 'send_otp',
          courtId: selectedCourt._id
        });
      }
    } catch (error) {
      console.error('Send OTP error:', error);
      setBookingError('Failed to send verification email. Please try again.');
      trackError(error, {
        step: 'send_otp',
        courtId: selectedCourt._id
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOTPVerification = async (otp) => {
    if (!pendingBookingData) return;

    // Track OTP verification attempt
    trackBookingFlow('otp_verification', {
      courtId: pendingBookingData.courtId,
      step: 5
    });

    setLoading(true);

    try {
      // Remove formData from payload and add OTP
      const { formData, ...apiPayload } = pendingBookingData;
      const otpPayload = {
        ...apiPayload,
        otp
      };

      const result = await bookingService.verifyBookingOTP(otpPayload);

      if (result.booking) {
        // Track successful booking completion
        trackBookingFlow('booking_completed', {
          bookingId: result.booking._id,
          courtId: result.booking.court._id || pendingBookingData.courtId,
          totalPrice: result.booking.totalPrice,
          step: 6
        });

        setBooking(result.booking);
        setShowConfirmation(true);
        setShowOTPVerification(false);
        setPendingBookingData(null);
        setBookingError(''); // Clear error on success

        // Add booking to state and refresh bookings list
        actions.addBooking(result.booking);
        actions.fetchUserBookings();
      } else {
        setBookingError(result.error?.message || 'Failed to create booking');
        setShowOTPVerification(false);
        trackError(new Error(result.error?.message || 'Failed to create booking'), {
          step: 'otp_verification',
          courtId: pendingBookingData.courtId
        });
      }
    } catch (error) {
      console.error('Booking error:', error);
      setBookingError('Failed to create booking. Please try again.');
      setShowOTPVerification(false);
      trackError(error, {
        step: 'otp_verification',
        courtId: pendingBookingData.courtId
      });
    } finally {
      setLoading(false);
    }
  };



  const goToStep = (step) => {
    if (step === 1) {
      setCurrentStep(1);
      setSelectedSlot(null);
    } else if (step === 2 && selectedCourt) {
      setCurrentStep(2);
      setSelectedSlot(null);
    } else if (step === 3 && selectedCourt && selectedDate && selectedSlot) {
      setCurrentStep(3);
    }
  };

  const resetBooking = () => {
    setCurrentStep(1);
    setSelectedCourt(null);
    setSelectedDate(null);
    setSelectedSlot(null);
    setBooking(null);
    setShowConfirmation(false);
    // Navigate to My Bookings page after successful booking
    navigate('/my-bookings');
  };

  return (
    <div className="min-h-screen bg-light-100">
      <div className="container mx-auto px-4 py-4 md:py-8 relative">
        {/* Loading Overlay */}
        {(loading || availabilityLoading) && (
          <LoadingSpinner
            overlay={true}
            text={loading ? "Processing booking..." : "Loading availability..."}
          />
        )}

        {/* Header */}
        <div className="mb-6 md:mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-dark-900 mb-2">Book a Padel Court</h1>
          <p className="text-dark-600 text-sm md:text-base">Select your court, pick a time, and book instantly</p>
        </div>

      {/* Progress Steps */}
      <div className="mb-8">
        {/* Desktop Steps */}
        <div className="hidden md:flex items-center justify-center space-x-8">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-center">
              <button
                onClick={() => goToStep(step.number)}
                className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                  currentStep === step.number
                    ? 'bg-primary-100 text-primary-700'
                    : currentStep > step.number
                    ? 'bg-success-100 text-success-700 cursor-pointer hover:bg-success-200'
                    : 'bg-light-200 text-dark-500'
                }`}
                disabled={
                  (step.number === 2 && !selectedCourt) ||
                  (step.number === 3 && (!selectedCourt || !selectedDate || !selectedSlot))
                }
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === step.number
                    ? 'bg-primary-600 text-light-50'
                    : currentStep > step.number
                    ? 'bg-success-600 text-light-50'
                    : 'bg-light-300 text-dark-600'
                }`}>
                  {currentStep > step.number ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    step.number
                  )}
                </div>
                <div className="text-left">
                  <div className="font-medium">{step.title}</div>
                  <div className="text-xs opacity-75">{step.description}</div>
                </div>
              </button>
              {index < steps.length - 1 && (
                <ArrowRight className="h-5 w-5 text-dark-400 mx-4" />
              )}
            </div>
          ))}
        </div>

        {/* Mobile Steps */}
        <div className="md:hidden">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              {currentStep > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentStep(currentStep - 1)}
                  disabled={loading}
                  className="flex items-center p-2"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              )}
              <h2 className="text-lg font-semibold text-dark-900">
                Step {currentStep} of {steps.length}
              </h2>
            </div>
            <div className="flex space-x-1">
              {steps.map((step) => (
                <div
                  key={step.number}
                  className={`w-3 h-3 rounded-full ${
                    currentStep === step.number
                      ? 'bg-primary-600'
                      : currentStep > step.number
                      ? 'bg-success-600'
                      : 'bg-light-300'
                  }`}
                />
              ))}
            </div>
          </div>
          <div className="bg-light-200 rounded-lg p-4">
            <h3 className="font-medium text-dark-900">{steps[currentStep - 1].title}</h3>
            <p className="text-sm text-dark-600 mt-1">{steps[currentStep - 1].description}</p>
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="space-y-6">
        {/* Step 1: Court Selection */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <CourtGrid
              courts={filteredCourts}
              onCourtSelect={handleCourtSelect}
              selectedCourtId={selectedCourt?._id}
              loading={state.loading}
            />
          </div>
        )}

        {/* Step 2: Date & Time Selection */}
        {currentStep === 2 && selectedCourt && (
          <div className="space-y-6">
            {/* Duration Selector */}
            <DurationSelector
              selectedDuration={selectedDuration}
              onDurationChange={setSelectedDuration}
            />

            {/* Date and Time Selection */}
            <div className="space-y-6 lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-6">
              <DatePicker
                selectedDate={selectedDate}
                onDateSelect={handleDateSelect}
              />
              <TimeSlotGrid
                timeSlots={timeSlots}
                selectedSlot={selectedSlot}
                onSlotSelect={handleSlotSelect}
                courtName={selectedCourt.name}
                selectedDate={selectedDate}
                loading={availabilityLoading}
                duration={selectedDuration}
              />
            </div>
          </div>
        )}

        {/* Step 3: Booking Form */}
        {currentStep === 3 && selectedCourt && selectedDate && selectedSlot && (
          <BookingForm
            court={selectedCourt}
            selectedDate={selectedDate}
            selectedSlot={selectedSlot}
            onSubmit={handleBookingSubmit}
            onCancel={() => setCurrentStep(2)}
            loading={loading}
            error={bookingError}
          />
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="mt-6 md:mt-8 flex justify-between">
        {currentStep > 1 && (
          <Button
            variant="outline"
            onClick={() => setCurrentStep(currentStep - 1)}
            disabled={loading}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            <span>Back</span>
          </Button>
        )}

        {currentStep < 3 && selectedCourt && (currentStep === 1 || (currentStep === 2 && selectedSlot)) && (
          <Button
            onClick={() => setCurrentStep(currentStep + 1)}
            disabled={loading}
            className="flex items-center ml-auto"
          >
            <span>Next</span>
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        )}
      </div>

      {/* OTP Verification Modal */}
      <OTPVerification
        isOpen={showOTPVerification}
        onClose={() => {
          setShowOTPVerification(false);
          setPendingBookingData(null);
        }}
        onVerify={handleOTPVerification}
        email={state.user?.email || '<EMAIL>'}
        loading={loading}
      />

      {/* Booking Confirmation Modal */}
      <Modal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        size="lg"
      >
        <BookingConfirmation
          booking={booking}
          onClose={resetBooking}
          onDownload={() => console.log('Download receipt')}
          onShare={() => console.log('Share booking')}
        />
      </Modal>
      </div>
    </div>
  );
};

export default BookingPage;
