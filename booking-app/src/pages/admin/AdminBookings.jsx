import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Plus,
  Calendar,
  Clock,
  MapPin,
  User,
  Phone,
  Mail,
  MoreVertical,
  AlertCircle
} from 'lucide-react';
import { Card, Button, Input, Select, Badge, Table, Modal } from '../../components/ui';
import { adminService } from '../../services/adminService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';



const AdminBookings = () => {
  const [bookings, setBookings] = useState([]);
  const [filteredBookings, setFilteredBookings] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [courtFilter, setCourtFilter] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  // Fetch bookings
  useEffect(() => {
    fetchBookings();
  }, [pagination.page, statusFilter, courtFilter, searchTerm]);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      const filters = {};

      if (statusFilter !== 'all') filters.status = statusFilter;
      if (courtFilter !== 'all') filters.courtId = courtFilter;
      if (searchTerm) filters.search = searchTerm;

      const result = await adminService.getAllBookings(
        pagination.page,
        pagination.limit,
        filters
      );

      if (result.error) {
        throw new Error(result.error.message || 'Failed to fetch bookings');
      }

      setBookings(result.bookings);
      setFilteredBookings(result.bookings);
      setPagination(prev => ({
        ...prev,
        total: result.pagination.total,
        pages: result.pagination.pages
      }));
      setError(null);
    } catch (err) {
      console.error('Fetch bookings error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const [showFilters, setShowFilters] = useState(false);

  // Update booking status
  const handleStatusUpdate = async (bookingId, newStatus) => {
    try {
      const result = await adminService.updateBookingStatus(bookingId, newStatus);

      if (result.error) {
        throw new Error(result.error.message || 'Failed to update booking status');
      }

      // Update local state
      setBookings(prev => prev.map(booking =>
        booking._id === bookingId ? { ...booking, status: newStatus } : booking
      ));
      setFilteredBookings(prev => prev.map(booking =>
        booking._id === bookingId ? { ...booking, status: newStatus } : booking
      ));

      alert('Booking status updated successfully');
    } catch (err) {
      console.error('Update booking status error:', err);
      alert('Failed to update booking status: ' + err.message);
    }
  };

  // Delete booking
  const handleDeleteBooking = async (bookingId) => {
    if (!confirm('Are you sure you want to delete this booking? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await adminService.deleteBooking(bookingId);

      if (result.error) {
        throw new Error(result.error.message || 'Failed to delete booking');
      }

      // Remove from local state
      setBookings(prev => prev.filter(booking => booking._id !== bookingId));
      setFilteredBookings(prev => prev.filter(booking => booking._id !== bookingId));

      alert('Booking deleted successfully');
    } catch (err) {
      console.error('Delete booking error:', err);
      alert('Failed to delete booking: ' + err.message);
    }
  };

  const getStatusConfig = (status) => {
    const configs = {
      confirmed: { variant: 'success', text: 'Confirmed' },
      pending: { variant: 'warning', text: 'Pending' },
      cancelled: { variant: 'error', text: 'Cancelled' },
      completed: { variant: 'default', text: 'Completed' }
    };
    return configs[status] || configs.pending;
  };

  const getPaymentStatusConfig = (status) => {
    const configs = {
      paid: { variant: 'success', text: 'Paid' },
      pending: { variant: 'warning', text: 'Pending' },
      refunded: { variant: 'error', text: 'Refunded' },
      failed: { variant: 'error', text: 'Failed' }
    };
    return configs[status] || configs.pending;
  };

  // Filter bookings based on search and filters
  const applyFilters = () => {
    let filtered = [...bookings];

    if (searchTerm) {
      filtered = filtered.filter(booking =>
        booking.customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(booking => booking.status === statusFilter);
    }

    if (courtFilter !== 'all') {
      filtered = filtered.filter(booking => booking.court.id === parseInt(courtFilter));
    }

    setFilteredBookings(filtered);
  };

  // Apply filters when dependencies change
  useState(() => {
    applyFilters();
  }, [searchTerm, statusFilter, courtFilter]);

  const handleViewDetails = (booking) => {
    setSelectedBooking(booking);
    setShowDetails(true);
  };

  const handleUpdateStatus = (bookingId, newStatus) => {
    // In a real app, this would make an API call
    console.log('Update booking status:', bookingId, newStatus);
  };

  const handleExport = () => {
    // In a real app, this would export the data
    console.log('Export bookings');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" text="Loading bookings..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Bookings</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => fetchBookings()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900">Manage Bookings</h1>
          <p className="text-secondary-600 mt-1">
            View and manage all court bookings
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Booking
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-500" />
                <Input
                  placeholder="Search bookings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>

          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-secondary-200">
              <Select
                label="Status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="confirmed">Confirmed</option>
                <option value="pending">Pending</option>
                <option value="cancelled">Cancelled</option>
                <option value="completed">Completed</option>
              </Select>

              <Select
                label="Court"
                value={courtFilter}
                onChange={(e) => setCourtFilter(e.target.value)}
              >
                <option value="all">All Courts</option>
                <option value="1">Court 1 - Premium</option>
                <option value="2">Court 2 - Standard</option>
                <option value="3">Court 3 - Training</option>
              </Select>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                    setCourtFilter('all');
                  }}
                  className="w-full"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Bookings Table */}
      <Card>
        <div className="overflow-x-auto">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.Head>Booking ID</Table.Head>
                <Table.Head>Customer</Table.Head>
                <Table.Head>Court</Table.Head>
                <Table.Head>Date & Time</Table.Head>
                <Table.Head>Players</Table.Head>
                <Table.Head>Amount</Table.Head>
                <Table.Head>Status</Table.Head>
                <Table.Head>Payment</Table.Head>
                <Table.Head>Actions</Table.Head>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {filteredBookings.map((booking) => (
                <Table.Row key={booking._id}>
                  <Table.Cell>
                    <div className="font-medium text-primary-600">
                      #{booking._id.slice(-8)}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      <div className="font-medium text-secondary-900">
                        {booking.customer?.firstName} {booking.customer?.lastName}
                      </div>
                      <div className="text-sm text-secondary-600">
                        {booking.customer?.email}
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      <div className="font-medium text-secondary-900">
                        {booking.court?.name}
                      </div>
                      <div className="text-sm text-secondary-600">
                        {booking.court?.location}
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      <div className="font-medium text-secondary-900">
                        {format(new Date(booking.date), 'MMM d, yyyy')}
                      </div>
                      <div className="text-sm text-secondary-600">
                        {booking.timeSlot?.startTime} - {booking.timeSlot?.endTime}
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <span className="font-medium">{booking.players}</span>
                  </Table.Cell>
                  <Table.Cell>
                    <span className="font-medium">Rs. {booking.totalPrice}</span>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge variant={getStatusConfig(booking.status).variant}>
                      {getStatusConfig(booking.status).text}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge variant={getPaymentStatusConfig(booking.paymentStatus).variant}>
                      {getPaymentStatusConfig(booking.paymentStatus).text}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetails(booking)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => console.log('Edit booking:', booking.id)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteBooking(booking._id)}
                        className="text-error-600 hover:text-error-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>

        {filteredBookings.length === 0 && (
          <div className="p-12 text-center">
            <Calendar className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-secondary-900 mb-2">No bookings found</h3>
            <p className="text-secondary-600">
              {searchTerm || statusFilter !== 'all' || courtFilter !== 'all'
                ? 'Try adjusting your search or filters.'
                : 'No bookings have been made yet.'}
            </p>
          </div>
        )}
      </Card>

      {/* Booking Details Modal */}
      <Modal
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
        title="Booking Details"
        size="lg"
      >
        {selectedBooking && (
          <div className="space-y-6">
            {/* Booking Header */}
            <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg">
              <div>
                <h3 className="text-lg font-semibold text-primary-900">
                  Booking #{selectedBooking.id}
                </h3>
                <p className="text-sm text-primary-700">
                  Created on {format(new Date(selectedBooking.createdAt), 'MMM d, yyyy')}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={getStatusConfig(selectedBooking.status).variant}>
                  {getStatusConfig(selectedBooking.status).text}
                </Badge>
                <Badge variant={getPaymentStatusConfig(selectedBooking.paymentStatus).variant}>
                  {getPaymentStatusConfig(selectedBooking.paymentStatus).text}
                </Badge>
              </div>
            </div>

            {/* Booking Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-secondary-900 flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Customer Information
                </h4>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-secondary-600">Name:</span>
                    <span className="ml-2 font-medium">
                      {selectedBooking.customer.firstName} {selectedBooking.customer.lastName}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-secondary-500 mr-2" />
                    <span>{selectedBooking.customer.email}</span>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 text-secondary-500 mr-2" />
                    <span>{selectedBooking.customer.phone}</span>
                  </div>
                </div>
              </div>

              {/* Booking Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-secondary-900 flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  Booking Information
                </h4>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-secondary-500 mr-2" />
                    <div>
                      <div className="font-medium">{selectedBooking.court.name}</div>
                      <div className="text-secondary-600">{selectedBooking.court.location}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-secondary-500 mr-2" />
                    <span>{format(new Date(selectedBooking.date), 'EEEE, MMMM d, yyyy')}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-secondary-500 mr-2" />
                    <span>{selectedBooking.timeSlot.startTime} - {selectedBooking.timeSlot.endTime}</span>
                  </div>
                  <div>
                    <span className="text-secondary-600">Players:</span>
                    <span className="ml-2 font-medium">{selectedBooking.players}</span>
                  </div>
                  <div>
                    <span className="text-secondary-600">Total Amount:</span>
                    <span className="ml-2 font-medium">${selectedBooking.totalPrice}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Special Requests */}
            {selectedBooking.specialRequests && (
              <div className="p-4 bg-warning-50 rounded-lg">
                <h4 className="font-medium text-warning-900 mb-2">Special Requests</h4>
                <p className="text-sm text-warning-800">{selectedBooking.specialRequests}</p>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-3 pt-4 border-t border-secondary-200">
              <Button
                variant="outline"
                onClick={() => handleUpdateStatus(selectedBooking.id, 'confirmed')}
                disabled={selectedBooking.status === 'confirmed'}
                className="flex-1"
              >
                Confirm Booking
              </Button>
              <Button
                variant="outline"
                onClick={() => handleUpdateStatus(selectedBooking.id, 'cancelled')}
                disabled={selectedBooking.status === 'cancelled'}
                className="flex-1 text-error-600 hover:text-error-700"
              >
                Cancel Booking
              </Button>
              <Button
                onClick={() => console.log('Edit booking:', selectedBooking.id)}
                className="flex-1"
              >
                Edit Booking
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AdminBookings;
