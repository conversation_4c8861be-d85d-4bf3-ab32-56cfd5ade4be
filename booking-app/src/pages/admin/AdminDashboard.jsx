import { useState, useEffect } from 'react';
import {
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  Clock,
  MapPin,
  Activity,
  AlertCircle
} from 'lucide-react';
import { <PERSON>, Badge, Button } from '../../components/ui';
import { format, startOfWeek, endOfWeek, isToday } from 'date-fns';
import { adminService } from '../../services/adminService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';



const mockTodaySchedule = [
  {
    id: 1,
    court: 'Court 1',
    time: '09:00 - 10:00',
    customer: '<PERSON>',
    status: 'confirmed'
  },
  {
    id: 2,
    court: 'Court 2',
    time: '10:00 - 11:00',
    customer: '<PERSON>',
    status: 'confirmed'
  },
  {
    id: 3,
    court: 'Court 1',
    time: '11:00 - 12:00',
    customer: '<PERSON>',
    status: 'pending'
  },
  {
    id: 4,
    court: 'Court 3',
    time: '14:00 - 15:00',
    customer: '<PERSON>',
    status: 'confirmed'
  }
];

const AdminDashboard = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  const [stats, setStats] = useState(null);
  const [recentBookings, setRecentBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch stats and recent bookings in parallel
        const [statsResult, bookingsResult] = await Promise.all([
          adminService.getDashboardStats(),
          adminService.getRecentBookings(5)
        ]);

        if (statsResult.error) {
          throw new Error(statsResult.error.message || 'Failed to fetch stats');
        }

        if (bookingsResult.error) {
          throw new Error(bookingsResult.error.message || 'Failed to fetch bookings');
        }

        setStats(statsResult.stats);
        setRecentBookings(bookingsResult.bookings);
        setError(null);
      } catch (err) {
        console.error('Dashboard fetch error:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const getStatusConfig = (status) => {
    const configs = {
      confirmed: { variant: 'success', text: 'Confirmed' },
      pending: { variant: 'warning', text: 'Pending' },
      cancelled: { variant: 'error', text: 'Cancelled' }
    };
    return configs[status] || configs.confirmed;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-dark-900 mb-2">Error Loading Dashboard</h2>
          <p className="text-dark-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900">Dashboard</h1>
          <p className="text-secondary-600 mt-1">
            Welcome back! Here's what's happening at your facility.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="input w-auto"
          >
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
          <Button>Export Report</Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Total Bookings</p>
                <p className="text-2xl font-bold text-secondary-900">{stats?.totalBookings || 0}</p>
              </div>
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-primary-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-success-600 mr-1" />
              <span className="text-sm text-success-600 font-medium">+12%</span>
              <span className="text-sm text-secondary-600 ml-1">from last week</span>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Revenue</p>
                <p className="text-2xl font-bold text-secondary-900">Rs. {stats?.totalRevenue?.toLocaleString() || 0}</p>
              </div>
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-success-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-success-600 mr-1" />
              <span className="text-sm text-success-600 font-medium">+8%</span>
              <span className="text-sm text-secondary-600 ml-1">from last week</span>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Active Bookings</p>
                <p className="text-2xl font-bold text-secondary-900">{stats?.activeBookings || 0}</p>
              </div>
              <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-warning-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <Activity className="h-4 w-4 text-warning-600 mr-1" />
              <span className="text-sm text-secondary-600">Currently playing</span>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-600">Court Utilization</p>
                <p className="text-2xl font-bold text-secondary-900">{stats?.courtUtilization || 0}%</p>
              </div>
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <MapPin className="h-6 w-6 text-primary-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-secondary-200 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full"
                  style={{ width: `${stats?.courtUtilization || 0}%` }}
                ></div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Bookings */}
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-secondary-900">Recent Bookings</h3>
              <Button variant="outline" size="sm">View All</Button>
            </div>
            <div className="space-y-4">
              {recentBookings.length > 0 ? recentBookings.map(booking => (
                <div key={booking._id} className="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-1">
                      <span className="font-medium text-secondary-900">
                        {booking.customer?.firstName} {booking.customer?.lastName}
                      </span>
                      <Badge variant={getStatusConfig(booking.status).variant} size="sm">
                        {getStatusConfig(booking.status).text}
                      </Badge>
                    </div>
                    <div className="text-sm text-secondary-600">
                      {booking.court?.name} • {booking.timeSlot?.startTime} - {booking.timeSlot?.endTime}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-secondary-900">Rs. {booking.totalPrice}</div>
                    <div className="text-sm text-secondary-600">
                      {new Date(booking.date).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              )) : (
                <div className="text-center py-8 text-dark-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-dark-300" />
                  <p>No recent bookings</p>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Today's Schedule */}
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-secondary-900">Today's Schedule</h3>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-secondary-500" />
                <span className="text-sm text-secondary-600">
                  {format(new Date(), 'EEEE, MMM d')}
                </span>
              </div>
            </div>
            <div className="space-y-3">
              {mockTodaySchedule.map(booking => (
                <div key={booking.id} className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    <div>
                      <div className="font-medium text-secondary-900">{booking.time}</div>
                      <div className="text-sm text-secondary-600">{booking.court}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-secondary-900">{booking.customer}</div>
                    <Badge variant={getStatusConfig(booking.status).variant} size="sm">
                      {getStatusConfig(booking.status).text}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold text-secondary-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="h-auto p-4 flex flex-col items-center space-y-2">
              <Calendar className="h-6 w-6" />
              <span>New Booking</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <MapPin className="h-6 w-6" />
              <span>Manage Courts</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <Clock className="h-6 w-6" />
              <span>Set Schedule</span>
            </Button>
          </div>
        </div>
      </Card>

      {/* Alerts */}
      <Card>
        <div className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <AlertCircle className="h-5 w-5 text-warning-600" />
            <h3 className="text-lg font-semibold text-secondary-900">System Alerts</h3>
          </div>
          <div className="space-y-3">
            <div className="p-3 bg-warning-50 border border-warning-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-warning-900">Court 2 maintenance due</div>
                  <div className="text-sm text-warning-700">Scheduled maintenance required by end of week</div>
                </div>
                <Button variant="outline" size="sm">Schedule</Button>
              </div>
            </div>
            <div className="p-3 bg-primary-50 border border-primary-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-primary-900">3 pending bookings</div>
                  <div className="text-sm text-primary-700">Require confirmation or payment</div>
                </div>
                <Button variant="outline" size="sm">Review</Button>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminDashboard;
