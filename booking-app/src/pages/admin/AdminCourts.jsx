import { useState } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  MapPin,
  Users,
  Star,
  DollarSign,
  Settings,
  Eye,
  EyeOff,
  Wifi,
  Car,
  Coffee,
  // Shower
} from 'lucide-react';
import { Card, Button, Input, Select, Badge, Modal, Table } from '../../components/ui';

// Mock courts data
const mockCourts = [
  {
    id: 1,
    name: 'Court 1 - Premium',
    description: 'Professional padel court with premium surface and lighting',
    location: 'Main Building',
    capacity: 4,
    pricePerHour: 45,
    rating: 4.8,
    status: 'active',
    amenities: ['WiFi', 'Parking', 'Showers', 'Equipment Rental'],
    specifications: {
      surface: 'Artificial Grass',
      lighting: 'LED Professional',
      size: '20m x 10m',
      walls: 'Tempered Glass'
    },
    maintenanceSchedule: 'Weekly',
    lastMaintenance: '2024-01-15',
    nextMaintenance: '2024-01-22'
  },
  {
    id: 2,
    name: 'Court 2 - Standard',
    description: 'Standard padel court perfect for casual games',
    location: 'Main Building',
    capacity: 4,
    pricePerHour: 35,
    rating: 4.5,
    status: 'active',
    amenities: ['Parking', 'Equipment Rental'],
    specifications: {
      surface: 'Artificial Grass',
      lighting: 'Standard LED',
      size: '20m x 10m',
      walls: 'Tempered Glass'
    },
    maintenanceSchedule: 'Bi-weekly',
    lastMaintenance: '2024-01-10',
    nextMaintenance: '2024-01-24'
  },
  {
    id: 3,
    name: 'Court 3 - Training',
    description: 'Training court with coaching facilities',
    location: 'Training Center',
    capacity: 4,
    pricePerHour: 40,
    rating: 4.6,
    status: 'maintenance',
    amenities: ['WiFi', 'Coaching', 'Equipment Rental'],
    specifications: {
      surface: 'Artificial Grass',
      lighting: 'LED Professional',
      size: '20m x 10m',
      walls: 'Tempered Glass'
    },
    maintenanceSchedule: 'Weekly',
    lastMaintenance: '2024-01-20',
    nextMaintenance: '2024-01-27'
  }
];

const AdminCourts = () => {
  const [courts, setCourts] = useState(mockCourts);
  const [selectedCourt, setSelectedCourt] = useState(null);
  const [showCourtModal, setShowCourtModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    location: '',
    capacity: 4,
    pricePerHour: 0,
    amenities: [],
    specifications: {
      surface: '',
      lighting: '',
      size: '',
      walls: ''
    },
    maintenanceSchedule: 'weekly'
  });

  const amenityIcons = {
    'WiFi': Wifi,
    'Parking': Car,
    'Cafe': Coffee,
    // 'Showers': Shower,
    'Equipment Rental': Settings,
    'Coaching': Users
  };

  const availableAmenities = ['WiFi', 'Parking', 'Cafe', 'Showers', 'Equipment Rental', 'Coaching'];

  const getStatusConfig = (status) => {
    const configs = {
      active: { variant: 'success', text: 'Active' },
      maintenance: { variant: 'warning', text: 'Maintenance' },
      inactive: { variant: 'error', text: 'Inactive' }
    };
    return configs[status] || configs.active;
  };

  const handleAddCourt = () => {
    setFormData({
      name: '',
      description: '',
      location: '',
      capacity: 4,
      pricePerHour: 0,
      amenities: [],
      specifications: {
        surface: '',
        lighting: '',
        size: '',
        walls: ''
      },
      maintenanceSchedule: 'weekly'
    });
    setIsEditing(false);
    setShowCourtModal(true);
  };

  const handleEditCourt = (court) => {
    setFormData({
      name: court.name,
      description: court.description,
      location: court.location,
      capacity: court.capacity,
      pricePerHour: court.pricePerHour,
      amenities: court.amenities,
      specifications: court.specifications,
      maintenanceSchedule: court.maintenanceSchedule
    });
    setSelectedCourt(court);
    setIsEditing(true);
    setShowCourtModal(true);
  };

  const handleDeleteCourt = (courtId) => {
    setCourts(courts.filter(court => court.id !== courtId));
  };

  const handleToggleStatus = (courtId) => {
    setCourts(courts.map(court =>
      court.id === courtId
        ? { ...court, status: court.status === 'active' ? 'inactive' : 'active' }
        : court
    ));
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();

    if (isEditing) {
      setCourts(courts.map(court =>
        court.id === selectedCourt.id
          ? { ...court, ...formData }
          : court
      ));
    } else {
      const newCourt = {
        ...formData,
        id: Math.max(...courts.map(c => c.id)) + 1,
        rating: 0,
        status: 'active',
        lastMaintenance: new Date().toISOString().split('T')[0],
        nextMaintenance: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      };
      setCourts([...courts, newCourt]);
    }

    setShowCourtModal(false);
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleAmenityToggle = (amenity) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900">Manage Courts</h1>
          <p className="text-secondary-600 mt-1">
            Add, edit, and manage your padel courts
          </p>
        </div>
        <Button onClick={handleAddCourt}>
          <Plus className="h-4 w-4 mr-2" />
          Add Court
        </Button>
      </div>

      {/* Courts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {courts.map(court => (
          <Card key={court.id} className="hover:shadow-medium transition-shadow">
            <div className="p-6">
              {/* Court Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-semibold text-secondary-900">{court.name}</h3>
                    <Badge variant={getStatusConfig(court.status).variant}>
                      {getStatusConfig(court.status).text}
                    </Badge>
                  </div>
                  <p className="text-sm text-secondary-600 mb-2">{court.description}</p>
                  <div className="flex items-center text-sm text-secondary-600">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{court.location}</span>
                  </div>
                </div>
              </div>

              {/* Court Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center p-3 bg-secondary-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Users className="h-4 w-4 text-secondary-600" />
                  </div>
                  <div className="text-sm font-medium text-secondary-900">{court.capacity}</div>
                  <div className="text-xs text-secondary-600">Max Players</div>
                </div>
                <div className="text-center p-3 bg-secondary-50 rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <DollarSign className="h-4 w-4 text-secondary-600" />
                  </div>
                  <div className="text-sm font-medium text-secondary-900">${court.pricePerHour}</div>
                  <div className="text-xs text-secondary-600">Per Hour</div>
                </div>
              </div>

              {/* Rating */}
              {court.rating > 0 && (
                <div className="flex items-center space-x-1 mb-4">
                  <Star className="h-4 w-4 text-warning-500 fill-current" />
                  <span className="text-sm font-medium text-secondary-900">{court.rating}</span>
                  <span className="text-sm text-secondary-600">rating</span>
                </div>
              )}

              {/* Amenities */}
              <div className="mb-4">
                <div className="text-sm font-medium text-secondary-900 mb-2">Amenities</div>
                <div className="flex flex-wrap gap-1">
                  {court.amenities.slice(0, 3).map(amenity => {
                    const Icon = amenityIcons[amenity] || Settings;
                    return (
                      <div key={amenity} className="flex items-center space-x-1 px-2 py-1 bg-primary-50 text-primary-700 rounded text-xs">
                        <Icon className="h-3 w-3" />
                        <span>{amenity}</span>
                      </div>
                    );
                  })}
                  {court.amenities.length > 3 && (
                    <div className="px-2 py-1 bg-secondary-100 text-secondary-600 rounded text-xs">
                      +{court.amenities.length - 3} more
                    </div>
                  )}
                </div>
              </div>

              {/* Maintenance Info */}
              <div className="mb-4 p-3 bg-warning-50 rounded-lg">
                <div className="text-sm font-medium text-warning-900 mb-1">Maintenance</div>
                <div className="text-xs text-warning-700">
                  Last: {court.lastMaintenance} | Next: {court.nextMaintenance}
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditCourt(court)}
                  className="flex-1"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleToggleStatus(court.id)}
                  className="flex-1"
                >
                  {court.status === 'active' ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-1" />
                      Disable
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-1" />
                      Enable
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteCourt(court.id)}
                  className="text-error-600 hover:text-error-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Add/Edit Court Modal */}
      <Modal
        isOpen={showCourtModal}
        onClose={() => setShowCourtModal(false)}
        title={isEditing ? 'Edit Court' : 'Add New Court'}
        size="lg"
      >
        <form onSubmit={handleFormSubmit} className="space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3">Basic Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Court Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
              />
              <Input
                label="Location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                required
              />
            </div>
            <div className="mt-4">
              <label className="label text-secondary-700 mb-2 block">Description</label>
              <textarea
                className="input min-h-[80px] resize-none"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe the court..."
                rows={3}
              />
            </div>
          </div>

          {/* Pricing and Capacity */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3">Pricing & Capacity</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Price per Hour ($)"
                type="number"
                value={formData.pricePerHour}
                onChange={(e) => handleInputChange('pricePerHour', parseFloat(e.target.value))}
                required
              />
              <Input
                label="Maximum Players"
                type="number"
                value={formData.capacity}
                onChange={(e) => handleInputChange('capacity', parseInt(e.target.value))}
                required
              />
            </div>
          </div>

          {/* Specifications */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3">Court Specifications</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Surface Type"
                value={formData.specifications.surface}
                onChange={(e) => handleInputChange('specifications.surface', e.target.value)}
                placeholder="e.g., Artificial Grass"
              />
              <Input
                label="Lighting"
                value={formData.specifications.lighting}
                onChange={(e) => handleInputChange('specifications.lighting', e.target.value)}
                placeholder="e.g., LED Professional"
              />
              <Input
                label="Court Size"
                value={formData.specifications.size}
                onChange={(e) => handleInputChange('specifications.size', e.target.value)}
                placeholder="e.g., 20m x 10m"
              />
              <Input
                label="Wall Type"
                value={formData.specifications.walls}
                onChange={(e) => handleInputChange('specifications.walls', e.target.value)}
                placeholder="e.g., Tempered Glass"
              />
            </div>
          </div>

          {/* Amenities */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3">Amenities</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {availableAmenities.map(amenity => {
                const Icon = amenityIcons[amenity] || Settings;
                const isSelected = formData.amenities.includes(amenity);
                return (
                  <button
                    key={amenity}
                    type="button"
                    onClick={() => handleAmenityToggle(amenity)}
                    className={`flex items-center space-x-2 p-3 rounded-lg border-2 transition-all ${
                      isSelected
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-secondary-200 bg-white text-secondary-700 hover:border-secondary-300'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="text-sm font-medium">{amenity}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Maintenance Schedule */}
          <div>
            <Select
              label="Maintenance Schedule"
              value={formData.maintenanceSchedule}
              onChange={(e) => handleInputChange('maintenanceSchedule', e.target.value)}
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="bi-weekly">Bi-weekly</option>
              <option value="monthly">Monthly</option>
            </Select>
          </div>

          {/* Form Actions */}
          <div className="flex space-x-3 pt-4 border-t border-secondary-200">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowCourtModal(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button type="submit" className="flex-1">
              {isEditing ? 'Update Court' : 'Add Court'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default AdminCourts;
