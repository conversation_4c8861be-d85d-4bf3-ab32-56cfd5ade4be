import { useState } from 'react';
import {
  Calendar,
  Clock,
  Plus,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Save,
  X,
  AlertCircle
} from 'lucide-react';
import { Card, Button, Input, Select, Badge, Modal } from '../../components/ui';
import { format, addDays, startOfWeek, endOfWeek, isSameDay, isToday, parseISO } from 'date-fns';

// Mock operating hours data
const mockOperatingHours = {
  monday: { open: '08:00', close: '22:00', isOpen: true },
  tuesday: { open: '08:00', close: '22:00', isOpen: true },
  wednesday: { open: '08:00', close: '22:00', isOpen: true },
  thursday: { open: '08:00', close: '22:00', isOpen: true },
  friday: { open: '08:00', close: '23:00', isOpen: true },
  saturday: { open: '09:00', close: '23:00', isOpen: true },
  sunday: { open: '09:00', close: '20:00', isOpen: true }
};

// Mock holidays and closures
const mockHolidays = [
  { id: 1, date: '2024-01-01', name: 'New Year\'s Day', isFullDay: true },
  { id: 2, date: '2024-12-25', name: 'Christmas Day', isFullDay: true },
  { id: 3, date: '2024-12-31', name: 'New Year\'s Eve', isFullDay: false, hours: { open: '08:00', close: '18:00' } }
];

// Mock court availability overrides
const mockOverrides = [
  { id: 1, date: '2024-01-25', courtId: 1, reason: 'Maintenance', isAvailable: false },
  { id: 2, date: '2024-01-26', courtId: 2, reason: 'Private Event', isAvailable: false },
  { id: 3, date: '2024-01-27', courtId: 3, reason: 'Tournament', isAvailable: false }
];

const AdminSchedule = () => {
  const [operatingHours, setOperatingHours] = useState(mockOperatingHours);
  const [holidays, setHolidays] = useState(mockHolidays);
  const [overrides, setOverrides] = useState(mockOverrides);
  const [currentWeek, setCurrentWeek] = useState(startOfWeek(new Date()));
  const [showHolidayModal, setShowHolidayModal] = useState(false);
  const [showOverrideModal, setShowOverrideModal] = useState(false);
  const [holidayForm, setHolidayForm] = useState({
    date: format(new Date(), 'yyyy-MM-dd'),
    name: '',
    isFullDay: true,
    hours: { open: '08:00', close: '18:00' }
  });
  const [overrideForm, setOverrideForm] = useState({
    date: format(new Date(), 'yyyy-MM-dd'),
    courtId: 1,
    reason: '',
    isAvailable: false
  });

  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeek, i));
  const weekStart = format(weekDays[0], 'MMM d, yyyy');
  const weekEnd = format(weekDays[6], 'MMM d, yyyy');

  const goToPreviousWeek = () => {
    setCurrentWeek(addDays(currentWeek, -7));
  };

  const goToNextWeek = () => {
    setCurrentWeek(addDays(currentWeek, 7));
  };

  const goToToday = () => {
    setCurrentWeek(startOfWeek(new Date()));
  };

  const handleOperatingHoursChange = (day, field, value) => {
    setOperatingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value
      }
    }));
  };

  const handleToggleDayOpen = (day) => {
    setOperatingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        isOpen: !prev[day].isOpen
      }
    }));
  };

  const handleAddHoliday = () => {
    const newHoliday = {
      ...holidayForm,
      id: Math.max(0, ...holidays.map(h => h.id)) + 1
    };
    setHolidays([...holidays, newHoliday]);
    setShowHolidayModal(false);
  };

  const handleDeleteHoliday = (id) => {
    setHolidays(holidays.filter(holiday => holiday.id !== id));
  };

  const handleAddOverride = () => {
    const newOverride = {
      ...overrideForm,
      id: Math.max(0, ...overrides.map(o => o.id)) + 1
    };
    setOverrides([...overrides, newOverride]);
    setShowOverrideModal(false);
  };

  const handleDeleteOverride = (id) => {
    setOverrides(overrides.filter(override => override.id !== id));
  };

  const isHoliday = (date) => {
    return holidays.some(holiday => holiday.date === format(date, 'yyyy-MM-dd'));
  };

  const getHolidayInfo = (date) => {
    return holidays.find(holiday => holiday.date === format(date, 'yyyy-MM-dd'));
  };

  const hasOverride = (date) => {
    return overrides.some(override => override.date === format(date, 'yyyy-MM-dd'));
  };

  const getOverridesForDate = (date) => {
    return overrides.filter(override => override.date === format(date, 'yyyy-MM-dd'));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900">Manage Schedule</h1>
          <p className="text-secondary-600 mt-1">
            Set operating hours, holidays, and court availability
          </p>
        </div>
      </div>

      {/* Operating Hours */}
      <Card>
        <div className="p-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-primary-600" />
            Operating Hours
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Object.entries(operatingHours).map(([day, hours]) => (
              <div
                key={day}
                className={`p-4 rounded-lg border-2 ${
                  hours.isOpen
                    ? 'border-secondary-200 bg-white'
                    : 'border-secondary-300 bg-secondary-100'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="font-medium text-secondary-900 capitalize">{day}</div>
                  <div>
                    <Badge variant={hours.isOpen ? 'success' : 'error'}>
                      {hours.isOpen ? 'Open' : 'Closed'}
                    </Badge>
                  </div>
                </div>

                {hours.isOpen ? (
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-xs text-secondary-600 mb-1 block">Open</label>
                      <Input
                        type="time"
                        value={hours.open}
                        onChange={(e) => handleOperatingHoursChange(day, 'open', e.target.value)}
                        className="text-sm"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-secondary-600 mb-1 block">Close</label>
                      <Input
                        type="time"
                        value={hours.close}
                        onChange={(e) => handleOperatingHoursChange(day, 'close', e.target.value)}
                        className="text-sm"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-2 text-secondary-600">
                    Closed all day
                  </div>
                )}

                <div className="mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleToggleDayOpen(day)}
                    className="w-full"
                  >
                    {hours.isOpen ? 'Mark as Closed' : 'Set as Open'}
                  </Button>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 flex justify-end">
            <Button>
              <Save className="h-4 w-4 mr-2" />
              Save Operating Hours
            </Button>
          </div>
        </div>
      </Card>

      {/* Holidays and Closures */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-secondary-900 flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-primary-600" />
              Holidays & Closures
            </h2>
            <Button onClick={() => setShowHolidayModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Holiday
            </Button>
          </div>

          {holidays.length === 0 ? (
            <div className="text-center py-8 bg-secondary-50 rounded-lg">
              <Calendar className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-900 mb-2">No holidays set</h3>
              <p className="text-secondary-600 mb-4">
                Add holidays or special closures to manage your facility's availability.
              </p>
              <Button onClick={() => setShowHolidayModal(true)}>
                Add Holiday
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {holidays.map(holiday => (
                <div key={holiday.id} className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg">
                  <div>
                    <div className="font-medium text-secondary-900">{holiday.name}</div>
                    <div className="text-sm text-secondary-600">
                      {format(parseISO(holiday.date), 'MMMM d, yyyy')}
                      {holiday.isFullDay ? (
                        <span className="ml-2 text-error-600">Closed all day</span>
                      ) : (
                        <span className="ml-2">
                          Open {holiday.hours.open} - {holiday.hours.close}
                        </span>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteHoliday(holiday.id)}
                    className="text-error-600 hover:text-error-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>

      {/* Weekly Schedule View */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-secondary-900 flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-primary-600" />
              Weekly Schedule
            </h2>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={goToToday}>
                Today
              </Button>
              <Button variant="outline" size="sm" onClick={goToPreviousWeek}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={goToNextWeek}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="text-center mb-4">
            <span className="text-lg font-medium text-secondary-900">
              {weekStart} - {weekEnd}
            </span>
          </div>

          <div className="grid grid-cols-7 gap-2">
            {weekDays.map((date) => {
              const dateStr = format(date, 'yyyy-MM-dd');
              const holiday = getHolidayInfo(date);
              const dateOverrides = getOverridesForDate(date);
              const isCurrentDay = isToday(date);

              return (
                <div key={dateStr} className="min-h-[150px]">
                  <div className={`text-center p-2 rounded-t-lg ${
                    isCurrentDay
                      ? 'bg-primary-100 text-primary-800'
                      : 'bg-secondary-100 text-secondary-800'
                  }`}>
                    <div className="text-xs font-medium">{format(date, 'EEE')}</div>
                    <div className="text-lg font-semibold">{format(date, 'd')}</div>
                  </div>

                  <div className="border border-t-0 border-secondary-200 rounded-b-lg p-2 h-full">
                    {holiday && (
                      <div className="mb-2 p-1 bg-error-100 text-error-800 rounded text-xs">
                        <div className="font-medium">{holiday.name}</div>
                        {holiday.isFullDay ? (
                          <div>Closed</div>
                        ) : (
                          <div>{holiday.hours.open} - {holiday.hours.close}</div>
                        )}
                      </div>
                    )}

                    {dateOverrides.length > 0 && (
                      <div className="space-y-1">
                        {dateOverrides.map(override => (
                          <div key={override.id} className="p-1 bg-warning-100 text-warning-800 rounded text-xs">
                            <div className="font-medium">Court {override.courtId}</div>
                            <div>{override.reason}</div>
                          </div>
                        ))}
                      </div>
                    )}

                    <div className="mt-2 flex justify-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setOverrideForm(prev => ({ ...prev, date: dateStr }));
                          setShowOverrideModal(true);
                        }}
                        className="text-xs h-7 px-2"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Override
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </Card>

      {/* Court Availability Overrides */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-secondary-900 flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-primary-600" />
              Court Availability Overrides
            </h2>
            <Button onClick={() => setShowOverrideModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Override
            </Button>
          </div>

          {overrides.length === 0 ? (
            <div className="text-center py-8 bg-secondary-50 rounded-lg">
              <AlertCircle className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-900 mb-2">No overrides set</h3>
              <p className="text-secondary-600 mb-4">
                Add overrides to mark specific courts as unavailable on certain dates.
              </p>
              <Button onClick={() => setShowOverrideModal(true)}>
                Add Override
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {overrides.map(override => (
                <div key={override.id} className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg">
                  <div>
                    <div className="font-medium text-secondary-900">Court {override.courtId}</div>
                    <div className="text-sm text-secondary-600">
                      {format(parseISO(override.date), 'MMMM d, yyyy')} - {override.reason}
                    </div>
                    <Badge variant={override.isAvailable ? 'success' : 'error'}>
                      {override.isAvailable ? 'Available' : 'Unavailable'}
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteOverride(override.id)}
                    className="text-error-600 hover:text-error-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>

      {/* Add Holiday Modal */}
      <Modal
        isOpen={showHolidayModal}
        onClose={() => setShowHolidayModal(false)}
        title="Add Holiday or Closure"
        size="md"
      >
        <div className="space-y-4">
          <Input
            label="Holiday Name"
            value={holidayForm.name}
            onChange={(e) => setHolidayForm(prev => ({ ...prev, name: e.target.value }))}
            placeholder="e.g., Christmas Day"
            required
          />

          <Input
            type="date"
            label="Date"
            value={holidayForm.date}
            onChange={(e) => setHolidayForm(prev => ({ ...prev, date: e.target.value }))}
            required
          />

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isFullDay"
              checked={holidayForm.isFullDay}
              onChange={(e) => setHolidayForm(prev => ({ ...prev, isFullDay: e.target.checked }))}
              className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
            />
            <label htmlFor="isFullDay" className="text-sm font-medium text-secondary-700">
              Closed all day
            </label>
          </div>

          {!holidayForm.isFullDay && (
            <div className="grid grid-cols-2 gap-4">
              <Input
                type="time"
                label="Opening Time"
                value={holidayForm.hours.open}
                onChange={(e) => setHolidayForm(prev => ({
                  ...prev,
                  hours: { ...prev.hours, open: e.target.value }
                }))}
              />
              <Input
                type="time"
                label="Closing Time"
                value={holidayForm.hours.close}
                onChange={(e) => setHolidayForm(prev => ({
                  ...prev,
                  hours: { ...prev.hours, close: e.target.value }
                }))}
              />
            </div>
          )}

          <div className="flex space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowHolidayModal(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddHoliday}
              className="flex-1"
              disabled={!holidayForm.name || !holidayForm.date}
            >
              Add Holiday
            </Button>
          </div>
        </div>
      </Modal>

      {/* Add Override Modal */}
      <Modal
        isOpen={showOverrideModal}
        onClose={() => setShowOverrideModal(false)}
        title="Add Court Override"
        size="md"
      >
        <div className="space-y-4">
          <Input
            type="date"
            label="Date"
            value={overrideForm.date}
            onChange={(e) => setOverrideForm(prev => ({ ...prev, date: e.target.value }))}
            required
          />

          <Select
            label="Court"
            value={overrideForm.courtId}
            onChange={(e) => setOverrideForm(prev => ({ ...prev, courtId: parseInt(e.target.value) }))}
          >
            <option value={1}>Court 1 - Premium</option>
            <option value={2}>Court 2 - Standard</option>
            <option value={3}>Court 3 - Training</option>
          </Select>

          <Input
            label="Reason"
            value={overrideForm.reason}
            onChange={(e) => setOverrideForm(prev => ({ ...prev, reason: e.target.value }))}
            placeholder="e.g., Maintenance, Tournament, etc."
            required
          />

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isAvailable"
              checked={overrideForm.isAvailable}
              onChange={(e) => setOverrideForm(prev => ({ ...prev, isAvailable: e.target.checked }))}
              className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
            />
            <label htmlFor="isAvailable" className="text-sm font-medium text-secondary-700">
              Court is available (unchecked means unavailable)
            </label>
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowOverrideModal(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddOverride}
              className="flex-1"
              disabled={!overrideForm.date || !overrideForm.reason}
            >
              Add Override
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AdminSchedule;
