import { apiRequest, tokenManager } from './api.js';

export const authService = {
  // Register new user (step 1 - send OTP)
  register: async (userData) => {
    const { data, error } = await apiRequest.post('/auth/register', userData);

    if (data && data.success) {
      return { data: data.data, error: null };
    }

    return { data: null, error };
  },

  // Verify email and complete registration (step 2)
  verifyEmailAndCompleteRegistration: async (verificationData) => {
    const { data, error } = await apiRequest.post('/auth/verify-email', verificationData);

    if (data && data.success) {
      const { accessToken, refreshToken, user } = data.data;
      tokenManager.setToken(accessToken);
      tokenManager.setRefreshToken(refreshToken);
      return { user, error: null };
    }

    return { user: null, error };
  },

  // Login user
  login: async (email, password) => {
    const { data, error } = await apiRequest.post('/auth/login', { email, password });
    
    if (data && data.success) {
      const { accessToken, refreshToken, user } = data.data;
      tokenManager.setToken(accessToken);
      tokenManager.setRefreshToken(refreshToken);
      return { user, error: null };
    }
    
    return { user: null, error };
  },

  // Logout user
  logout: async () => {
    const { data, error } = await apiRequest.post('/auth/logout');
    tokenManager.clearTokens();
    return { success: !error, error };
  },

  // Get current user profile
  getProfile: async () => {
    const { data, error } = await apiRequest.get('/auth/profile');
    
    if (data && data.success) {
      return { user: data.data.user, error: null };
    }
    
    return { user: null, error };
  },

  // Update user profile
  updateProfile: async (profileData) => {
    const { data, error } = await apiRequest.put('/auth/profile', profileData);

    if (data && data.success) {
      return { success: true, user: data.data.user, error: null };
    }

    return { success: false, user: null, error: error?.message || 'Failed to update profile' };
  },

  // Change password
  changePassword: async (passwordData) => {
    const { data, error } = await apiRequest.put('/auth/change-password', passwordData);

    if (data && data.success) {
      return { success: true, error: null };
    }

    return { success: false, error: error?.message || 'Failed to change password' };
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!tokenManager.getToken();
  },

  // Forgot password
  forgotPassword: async (email) => {
    const { data, error } = await apiRequest.post('/auth/forgot-password', { email });
    return { success: data?.success || false, error };
  },

  // Reset password
  resetPassword: async (email, otp, newPassword) => {
    const { data, error } = await apiRequest.post('/auth/reset-password', {
      email,
      otp,
      newPassword
    });
    return { success: data?.success || false, error };
  },

  // Get stored user data (if available)
  getCurrentUser: async () => {
    if (!authService.isAuthenticated()) {
      return { user: null, error: null };
    }

    try {
      return await authService.getProfile();
    } catch (error) {
      tokenManager.clearTokens();
      return { user: null, error };
    }
  }
};

export default authService;
