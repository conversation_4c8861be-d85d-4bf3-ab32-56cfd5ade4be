import { apiRequest } from './api';

export const adminService = {
  // Dashboard Analytics
  getDashboardStats: async () => {
    const { data, error } = await apiRequest.get('/admin/dashboard/stats');
    
    if (data && data.success) {
      return { stats: data.data, error: null };
    }
    
    return { stats: null, error };
  },

  getRecentBookings: async (limit = 10) => {
    const { data, error } = await apiRequest.get(`/admin/bookings/recent?limit=${limit}`);
    
    if (data && data.success) {
      return { bookings: data.data.bookings, error: null };
    }
    
    return { bookings: [], error };
  },

  // Booking Management
  getAllBookings: async (page = 1, limit = 20, filters = {}) => {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    const { data, error } = await apiRequest.get(`/admin/bookings?${queryParams}`);
    
    if (data && data.success) {
      return { 
        bookings: data.data.bookings, 
        pagination: data.data.pagination,
        error: null 
      };
    }
    
    return { bookings: [], pagination: null, error };
  },

  updateBookingStatus: async (bookingId, status) => {
    const { data, error } = await apiRequest.patch(`/admin/bookings/${bookingId}/status`, { status });
    
    if (data && data.success) {
      return { booking: data.data.booking, error: null };
    }
    
    return { booking: null, error };
  },

  deleteBooking: async (bookingId) => {
    const { data, error } = await apiRequest.delete(`/admin/bookings/${bookingId}`);
    
    if (data && data.success) {
      return { success: true, error: null };
    }
    
    return { success: false, error };
  },

  // Court Management
  getAllCourts: async () => {
    const { data, error } = await apiRequest.get('/admin/courts');
    
    if (data && data.success) {
      return { courts: data.data.courts, error: null };
    }
    
    return { courts: [], error };
  },

  createCourt: async (courtData) => {
    const { data, error } = await apiRequest.post('/admin/courts', courtData);
    
    if (data && data.success) {
      return { court: data.data.court, error: null };
    }
    
    return { court: null, error };
  },

  updateCourt: async (courtId, courtData) => {
    const { data, error } = await apiRequest.patch(`/admin/courts/${courtId}`, courtData);
    
    if (data && data.success) {
      return { court: data.data.court, error: null };
    }
    
    return { court: null, error };
  },

  deleteCourt: async (courtId) => {
    const { data, error } = await apiRequest.delete(`/admin/courts/${courtId}`);
    
    if (data && data.success) {
      return { success: true, error: null };
    }
    
    return { success: false, error };
  },

  uploadCourtImage: async (courtId, imageFile) => {
    const formData = new FormData();
    formData.append('image', imageFile);

    const { data, error } = await apiRequest.post(`/admin/courts/${courtId}/upload-image`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    if (data && data.success) {
      return { imageUrl: data.data.imageUrl, error: null };
    }
    
    return { imageUrl: null, error };
  },

  // Operating Hours Management
  getOperatingHours: async () => {
    const { data, error } = await apiRequest.get('/admin/operating-hours');
    
    if (data && data.success) {
      return { operatingHours: data.data.operatingHours, error: null };
    }
    
    return { operatingHours: [], error };
  },

  updateOperatingHours: async (dayOfWeek, hoursData) => {
    const { data, error } = await apiRequest.patch(`/admin/operating-hours/${dayOfWeek}`, hoursData);
    
    if (data && data.success) {
      return { operatingHours: data.data.operatingHours, error: null };
    }
    
    return { operatingHours: null, error };
  },

  // User Management
  getAllUsers: async (page = 1, limit = 20, filters = {}) => {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    const { data, error } = await apiRequest.get(`/admin/users?${queryParams}`);
    
    if (data && data.success) {
      return { 
        users: data.data.users, 
        pagination: data.data.pagination,
        error: null 
      };
    }
    
    return { users: [], pagination: null, error };
  },

  updateUserStatus: async (userId, status) => {
    const { data, error } = await apiRequest.patch(`/admin/users/${userId}/status`, { status });
    
    if (data && data.success) {
      return { user: data.data.user, error: null };
    }
    
    return { user: null, error };
  },

  // Analytics
  getBookingAnalytics: async (startDate, endDate) => {
    const queryParams = new URLSearchParams({
      startDate,
      endDate
    });

    const { data, error } = await apiRequest.get(`/admin/analytics/bookings?${queryParams}`);
    
    if (data && data.success) {
      return { analytics: data.data, error: null };
    }
    
    return { analytics: null, error };
  },

  getRevenueAnalytics: async (startDate, endDate) => {
    const queryParams = new URLSearchParams({
      startDate,
      endDate
    });

    const { data, error } = await apiRequest.get(`/admin/analytics/revenue?${queryParams}`);
    
    if (data && data.success) {
      return { analytics: data.data, error: null };
    }
    
    return { analytics: null, error };
  },

  // Settings
  getSettings: async () => {
    const { data, error } = await apiRequest.get('/admin/settings');
    
    if (data && data.success) {
      return { settings: data.data.settings, error: null };
    }
    
    return { settings: null, error };
  },

  updateSettings: async (settings) => {
    const { data, error } = await apiRequest.patch('/admin/settings', settings);
    
    if (data && data.success) {
      return { settings: data.data.settings, error: null };
    }
    
    return { settings: null, error };
  }
};
