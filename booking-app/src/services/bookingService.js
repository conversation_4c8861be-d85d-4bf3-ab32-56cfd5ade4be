import { apiRequest } from './api.js';

export const bookingService = {
  // Send booking OTP
  sendBookingOTP: async (bookingData) => {
    const { data, error } = await apiRequest.post('/bookings/send-otp', bookingData);

    if (data && data.success) {
      return { success: true, message: data.message, error: null };
    }

    return { success: false, message: null, error };
  },

  // Verify booking OTP and create booking
  verifyBookingOTP: async (otpData) => {
    const { data, error } = await apiRequest.post('/bookings/verify-otp', otpData);

    if (data && data.success) {
      return { booking: data.data.booking, error: null };
    }

    return { booking: null, error };
  },

  // Send cancellation OTP
  sendCancellationOTP: async (bookingId) => {
    const { data, error } = await apiRequest.post('/bookings/send-cancellation-otp', { bookingId });

    if (data && data.success) {
      return { success: true, message: data.message, error: null };
    }

    return { success: false, message: null, error };
  },

  // Verify cancellation OTP and cancel booking
  verifyCancellationOTP: async (otpData) => {
    const { data, error } = await apiRequest.post('/bookings/verify-cancellation-otp', otpData);

    if (data && data.success) {
      return { booking: data.data.booking, error: null };
    }

    return { booking: null, error };
  },

  // Create new booking (direct - for admin use)
  createBooking: async (bookingData) => {
    const { data, error } = await apiRequest.post('/bookings', bookingData);

    if (data && data.success) {
      return { booking: data.data.booking, error: null };
    }

    return { booking: null, error };
  },

  // Get user's bookings
  getUserBookings: async (filters = {}) => {
    const queryParams = new URLSearchParams();
    
    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value);
      }
    });

    const queryString = queryParams.toString();
    const url = queryString ? `/bookings/my-bookings?${queryString}` : '/bookings/my-bookings';
    
    const { data, error } = await apiRequest.get(url);
    
    if (data && data.success) {
      return { 
        bookings: data.data.bookings, 
        pagination: data.data.pagination,
        error: null 
      };
    }
    
    return { bookings: [], pagination: null, error };
  },

  // Get all bookings (admin only)
  getAllBookings: async (filters = {}) => {
    const queryParams = new URLSearchParams();
    
    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value);
      }
    });

    const queryString = queryParams.toString();
    const url = queryString ? `/bookings?${queryString}` : '/bookings';
    
    const { data, error } = await apiRequest.get(url);
    
    if (data && data.success) {
      return { 
        bookings: data.data.bookings, 
        pagination: data.data.pagination,
        error: null 
      };
    }
    
    return { bookings: [], pagination: null, error };
  },

  // Get booking by ID
  getBookingById: async (bookingId) => {
    const { data, error } = await apiRequest.get(`/bookings/${bookingId}`);
    
    if (data && data.success) {
      return { booking: data.data.booking, error: null };
    }
    
    return { booking: null, error };
  },

  // Cancel booking
  cancelBooking: async (bookingId, reason = '') => {
    const { data, error } = await apiRequest.patch(`/bookings/${bookingId}/cancel`, { reason });
    
    if (data && data.success) {
      return { booking: data.data.booking, error: null };
    }
    
    return { booking: null, error };
  },

  // Update booking status (admin only)
  updateBookingStatus: async (bookingId, status, notes = '') => {
    const { data, error } = await apiRequest.patch(`/bookings/${bookingId}/status`, { 
      status, 
      notes 
    });
    
    if (data && data.success) {
      return { booking: data.data.booking, error: null };
    }
    
    return { booking: null, error };
  },

  // Get upcoming bookings
  getUpcomingBookings: async () => {
    return bookingService.getUserBookings({ upcoming: 'true' });
  },

  // Get booking history
  getBookingHistory: async (page = 1, limit = 10) => {
    return bookingService.getUserBookings({ page, limit });
  },

  // Check if time slot is available
  checkTimeSlotAvailability: async (courtId, date, timeSlot) => {
    try {
      // Import courtService here to avoid circular dependency
      const { courtService } = await import('./courtService.js');
      const { availability, error } = await courtService.getCourtAvailability(courtId, date);

      if (error) {
        return { available: false, error };
      }

      if (!availability.available) {
        return { available: false, reason: availability.reason };
      }

      // Check if the specific time slot is available
      const isSlotAvailable = availability.timeSlots.some(slot =>
        slot.startTime === timeSlot.startTime && slot.available
      );

      return {
        available: isSlotAvailable,
        reason: isSlotAvailable ? null : 'Time slot is not available',
        error: null
      };
    } catch (error) {
      return { available: false, error };
    }
  }
};

export default bookingService;
