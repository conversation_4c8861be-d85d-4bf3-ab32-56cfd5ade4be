import { apiRequest } from './api.js';

export const courtService = {
  // Get all courts with optional filters
  getCourts: async (filters = {}) => {
    const queryParams = new URLSearchParams();
    
    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(v => queryParams.append(key, v));
        } else {
          queryParams.append(key, value);
        }
      }
    });

    const queryString = queryParams.toString();
    const url = queryString ? `/courts?${queryString}` : '/courts';
    
    const { data, error } = await apiRequest.get(url);
    
    if (data && data.success) {
      return { 
        courts: data.data.courts, 
        pagination: data.data.pagination,
        error: null 
      };
    }
    
    return { courts: [], pagination: null, error };
  },

  // Get court by ID
  getCourtById: async (courtId) => {
    const { data, error } = await apiRequest.get(`/courts/${courtId}`);
    
    if (data && data.success) {
      return { court: data.data.court, error: null };
    }
    
    return { court: null, error };
  },

  // Get court availability for a specific date
  getCourtAvailability: async (courtId, date, duration = 60) => {
    const { data, error } = await apiRequest.get(`/courts/${courtId}/availability?date=${date}&duration=${duration}`);

    if (data && data.success) {
      return {
        availability: data.data,
        timeSlots: data.data.timeSlots,
        error: null
      };
    }

    return { availability: null, timeSlots: [], error };
  },

  // Create new court (admin only)
  createCourt: async (courtData) => {
    const { data, error } = await apiRequest.post('/courts', courtData);
    
    if (data && data.success) {
      return { court: data.data.court, error: null };
    }
    
    return { court: null, error };
  },

  // Update court (admin only)
  updateCourt: async (courtId, courtData) => {
    const { data, error } = await apiRequest.put(`/courts/${courtId}`, courtData);
    
    if (data && data.success) {
      return { court: data.data.court, error: null };
    }
    
    return { court: null, error };
  },

  // Delete court (admin only)
  deleteCourt: async (courtId) => {
    const { data, error } = await apiRequest.delete(`/courts/${courtId}`);
    
    return { success: data?.success || false, error };
  },

  // Search courts
  searchCourts: async (searchTerm, filters = {}) => {
    const searchFilters = {
      ...filters,
      search: searchTerm
    };
    
    return courtService.getCourts(searchFilters);
  },


};

export default courtService;
