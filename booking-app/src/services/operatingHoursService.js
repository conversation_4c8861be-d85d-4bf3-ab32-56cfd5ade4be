import { apiRequest } from './api.js';

export const operatingHoursService = {
  // Get all operating hours
  getOperatingHours: async () => {
    const { data, error } = await apiRequest.get('/operating-hours');
    
    if (data && data.success) {
      return { schedule: data.data.schedule, error: null };
    }
    
    return { schedule: null, error };
  },

  // Get operating hours for a specific day
  getOperatingHoursByDay: async (day) => {
    const { data, error } = await apiRequest.get(`/operating-hours/${day.toLowerCase()}`);
    
    if (data && data.success) {
      return { hours: data.data, error: null };
    }
    
    return { hours: null, error };
  },

  // Get available time slots for a specific day
  getAvailableSlots: async (day, duration = 60) => {
    const { data, error } = await apiRequest.get(`/operating-hours/${day.toLowerCase()}/slots?duration=${duration}`);
    
    if (data && data.success) {
      return { slots: data.data, error: null };
    }
    
    return { slots: null, error };
  },

  // Update operating hours for a specific day (admin only)
  updateOperatingHours: async (day, hoursData) => {
    const { data, error } = await apiRequest.put(`/operating-hours/${day.toLowerCase()}`, hoursData);
    
    if (data && data.success) {
      return { hours: data.data, error: null };
    }
    
    return { hours: null, error };
  },

  // Update multiple days at once (admin only)
  updateMultipleOperatingHours: async (schedule) => {
    const { data, error } = await apiRequest.put('/operating-hours', { schedule });
    
    if (data && data.success) {
      return { schedule: data.data.schedule, error: null };
    }
    
    return { schedule: null, error };
  },

  // Check if facility is open on a specific day and time
  isOpenAt: async (day, time) => {
    const { hours, error } = await operatingHoursService.getOperatingHoursByDay(day);
    
    if (error || !hours) {
      return { isOpen: false, error };
    }

    if (!hours.isOpen) {
      return { isOpen: false, reason: 'Facility is closed on this day' };
    }

    // Simple time comparison (you might want to make this more robust)
    const timeMinutes = timeToMinutes(time);
    const openMinutes = timeToMinutes(hours.openTime);
    const closeMinutes = timeToMinutes(hours.closeTime);

    const isOpen = timeMinutes >= openMinutes && timeMinutes < closeMinutes;
    
    return { 
      isOpen, 
      reason: isOpen ? null : 'Outside operating hours',
      hours: {
        openTime: hours.openTime,
        closeTime: hours.closeTime
      }
    };
  },

  // Get today's operating hours
  getTodayHours: async () => {
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    return operatingHoursService.getOperatingHoursByDay(today);
  }
};

// Helper function to convert time string to minutes
const timeToMinutes = (timeString) => {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
};

export default operatingHoursService;
