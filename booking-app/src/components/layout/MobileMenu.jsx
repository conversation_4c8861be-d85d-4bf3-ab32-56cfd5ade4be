import { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Calendar,
  Clock,
  MapPin,
  Menu,
  Settings,
  User,
  X,
  LogOut,
  Info,
  Phone,
  FileText,
  Shield,
  RotateCcw,
  ChevronRight,
  Globe,
  Mail,
  MessageCircle,
  Share2
} from 'lucide-react';
import { Button } from '../ui';

const MobileMenu = ({ isAdmin, userIsAdmin, isAuthenticated, user, logout }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  const handleAuthModal = (mode) => {
    // Dispatch custom event for auth modal
    window.dispatchEvent(new CustomEvent('openAuthModal', {
      detail: { mode }
    }));
    closeMenu();
  };

  // Navigation sections
  const mainNavigation = isAuthenticated && !isAdmin ? [
    { name: 'Book Courts', href: '/booking', icon: Calendar, color: 'text-blue-600', bgColor: 'bg-blue-50' },
    { name: 'My Bookings', href: '/my-bookings', icon: User, color: 'text-green-600', bgColor: 'bg-green-50' },
    { name: 'Settings', href: '/settings', icon: Settings, color: 'text-gray-600', bgColor: 'bg-gray-50' }
  ] : [];

  const adminNavigation = isAuthenticated && isAdmin && userIsAdmin ? [
    { name: 'Dashboard', href: '/admin', icon: Calendar, color: 'text-purple-600', bgColor: 'bg-purple-50' },
    { name: 'Bookings', href: '/admin/bookings', icon: Calendar, color: 'text-blue-600', bgColor: 'bg-blue-50' },
    { name: 'Courts', href: '/admin/courts', icon: MapPin, color: 'text-green-600', bgColor: 'bg-green-50' },
    { name: 'Schedule', href: '/admin/schedule', icon: Clock, color: 'text-orange-600', bgColor: 'bg-orange-50' },
    { name: 'Back to Booking', href: '/booking', icon: Calendar, color: 'text-blue-600', bgColor: 'bg-blue-50' }
  ] : [];

  const publicNavigation = [
    { name: 'About Us', href: '/about', icon: Info, color: 'text-blue-600', bgColor: 'bg-blue-50' },
    { name: 'Contact Us', href: '/contact', icon: Phone, color: 'text-green-600', bgColor: 'bg-green-50' },
    { name: 'Terms & Conditions', href: '/terms', icon: FileText, color: 'text-gray-600', bgColor: 'bg-gray-50' },
    { name: 'Privacy Policy', href: '/privacy', icon: Shield, color: 'text-gray-600', bgColor: 'bg-gray-50' },
    { name: 'Refund Policy', href: '/refund-policy', icon: RotateCcw, color: 'text-gray-600', bgColor: 'bg-gray-50' }
  ];

  return (
    <>
      {/* Menu Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMenu}
        className="p-2 relative"
        aria-label="Toggle menu"
      >
        {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 transition-opacity duration-300"
          onClick={closeMenu}
        />
      )}

      {/* Slide-out Menu */}
      <div className={`fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl transform transition-all duration-300 ease-out z-50 flex flex-col ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between flex-shrink-0" 
             style={{ paddingTop: `calc(1rem + var(--safe-area-inset-top))` }}>
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <img
              src="/icons/icon-full.png"
              alt="Padel Chase"
              className="w-12 h-6"
            />
          </div>

          {/* Close Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={closeMenu}
            className="text-gray-500 hover:bg-gray-100 p-2 rounded-full transition-colors"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Navigation Content - Scrollable */}
        <div className="flex-1 overflow-y-auto">
          <div className="py-4 min-h-full">
            {/* Main Navigation */}
            {(mainNavigation.length > 0 || adminNavigation.length > 0) && (
              <div className="px-6 mb-6">
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                  {isAdmin ? 'Admin Panel' : 'Booking'}
                </h3>
                <div className="space-y-2">
                  {(isAdmin ? adminNavigation : mainNavigation).map((item) => (
                    <Link
                      key={item.name}
                      to={item.href}
                      className="flex items-center px-3 py-3 rounded-xl hover:bg-gray-50 transition-all duration-200 group"
                      onClick={closeMenu}
                    >
                      <div className={`p-2 rounded-lg ${item.bgColor} mr-3 group-hover:scale-110 transition-transform duration-200`}>
                        <item.icon className={`h-4 w-4 ${item.color}`} />
                      </div>
                      <span className="font-medium text-gray-700 group-hover:text-gray-900">{item.name}</span>
                      <ChevronRight className="h-4 w-4 text-gray-400 ml-auto group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-200" />
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Public Pages */}
            <div className="px-6 pb-4">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                Pages
              </h3>
              <div className="space-y-2">
                {publicNavigation.map((item) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className="flex items-center px-3 py-3 rounded-xl hover:bg-gray-50 transition-all duration-200 group"
                    onClick={closeMenu}
                  >
                    <div className={`p-2 rounded-lg ${item.bgColor} mr-3 group-hover:scale-110 transition-transform duration-200`}>
                      <item.icon className={`h-4 w-4 ${item.color}`} />
                    </div>
                    <span className="font-medium text-gray-700 group-hover:text-gray-900">{item.name}</span>
                    <ChevronRight className="h-4 w-4 text-gray-400 ml-auto group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-200" />
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* User Card & Auth Section - Fixed at bottom */}
        <div className="border-t border-gray-200 bg-gray-50 flex-shrink-0"
             style={{ paddingBottom: `calc(1rem + var(--safe-area-inset-bottom))` }}>
          {isAuthenticated ? (
            <>
              {/* User Info Card - Hidden on screens < 700px */}
              <div className="px-6 py-3 hidden min-[700px]:block border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">{user?.firstName} {user?.lastName}</p>
                    <p className="text-xs text-gray-500 truncate">{user?.email}</p>
                  </div>
                </div>
              </div>

              {/* Social Media Icons & Actions */}
              <div className="px-6 py-4">
                {/* Social Media Icons */}
                {/* <div className="flex justify-center space-x-3 mb-4">
                  <a href="#" className="w-9 h-9 bg-blue-100 rounded-full flex items-center justify-center hover:bg-blue-200 transition-colors">
                    <Globe className="h-4 w-4 text-blue-600" />
                  </a>
                  <a href="#" className="w-9 h-9 bg-green-100 rounded-full flex items-center justify-center hover:bg-green-200 transition-colors">
                    <MessageCircle className="h-4 w-4 text-green-600" />
                  </a>
                  <a href="#" className="w-9 h-9 bg-purple-100 rounded-full flex items-center justify-center hover:bg-purple-200 transition-colors">
                    <Mail className="h-4 w-4 text-purple-600" />
                  </a>
                  <a href="#" className="w-9 h-9 bg-orange-100 rounded-full flex items-center justify-center hover:bg-orange-200 transition-colors">
                    <Share2 className="h-4 w-4 text-orange-600" />
                  </a>
                </div> */}

                {/* Action Buttons */}
                <Button
                  variant="outline"
                  className="w-full h-10 font-medium border-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
                  onClick={() => {
                    logout();
                    closeMenu();
                  }}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Logout
                </Button>
              </div>
            </>
          ) : (
            <div className="px-6 py-4">
              {/* Social Media Icons */}
              <div className="flex justify-center space-x-3 mb-4">
                <a href="#" className="w-9 h-9 bg-blue-100 rounded-full flex items-center justify-center hover:bg-blue-200 transition-colors">
                  <Globe className="h-4 w-4 text-blue-600" />
                </a>
                <a href="#" className="w-9 h-9 bg-green-100 rounded-full flex items-center justify-center hover:bg-green-200 transition-colors">
                  <MessageCircle className="h-4 w-4 text-green-600" />
                </a>
                <a href="#" className="w-9 h-9 bg-purple-100 rounded-full flex items-center justify-center hover:bg-purple-200 transition-colors">
                  <Mail className="h-4 w-4 text-purple-600" />
                </a>
                <a href="#" className="w-9 h-9 bg-orange-100 rounded-full flex items-center justify-center hover:bg-orange-200 transition-colors">
                  <Share2 className="h-4 w-4 text-orange-600" />
                </a>
              </div>

              <div className="space-y-2">
                <Button
                  variant="outline"
                  className="w-full h-10 font-medium border-2 hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
                  onClick={() => handleAuthModal('login')}
                >
                  <User className="h-4 w-4 mr-2" />
                  Login
                </Button>
                <Button
                  className="w-full h-10 font-medium bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-200"
                  onClick={() => handleAuthModal('register')}
                >
                  Get Started
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default MobileMenu;
