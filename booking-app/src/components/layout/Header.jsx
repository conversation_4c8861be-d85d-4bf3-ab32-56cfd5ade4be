import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Settings, User, LogOut } from "lucide-react";
import Button from "../ui/Button";
import MobileMenu from "./MobileMenu";
import AuthModal from "../auth/AuthModal";
import { useAuth } from "../../hooks/useAuth";

const Header = () => {
  const location = useLocation();
  const { user, isAuthenticated, logout, isAdmin: userIsAdmin } = useAuth();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState("login");

  const isAdmin = location.pathname.startsWith("/admin");

  // Listen for mobile menu auth events
  useEffect(() => {
    const handleAuthModal = (event) => {
      setAuthMode(event.detail.mode);
      setAuthModalOpen(true);
    };

    window.addEventListener('openAuthModal', handleAuthModal);
    return () => window.removeEventListener('openAuthModal', handleAuthModal);
  }, []);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" 
           style={{ paddingTop: 'var(--safe-area-inset-top)' }}>
        <div className="grid grid-cols-3 items-center h-14 md:h-16">
          {/* Left: Menu Button */}
          <div className="flex justify-start">
            <MobileMenu isAdmin={isAdmin} userIsAdmin={userIsAdmin} isAuthenticated={isAuthenticated} user={user} logout={logout} />
          </div>

          {/* Center: Logo */}
          <div className="flex justify-center">
            <Link to="/" className="flex items-center space-x-2">
              <div className="rounded-lg flex items-center justify-center">
                <img
                  src="/icons/icon-full.png"
                  alt="Padel Chase Logo"
                  className="w-16 h-8 md:w-20 md:h-10"
                />
              </div>
            </Link>
          </div>

          {/* Right: Actions */}
          <div className="flex justify-end">
            {!isAuthenticated ? (
              <div className="hidden min-[950px]:flex items-center space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setAuthMode("login");
                    setAuthModalOpen(true);
                  }}
                >
                  Login
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setAuthMode("register");
                    setAuthModalOpen(true);
                  }}
                >
                  <User className="h-4 w-4 mr-2" />
                  Sign Up
                </Button>
              </div>
            ) : (
              <div className="hidden min-[950px]:flex items-center space-x-3">
                {userIsAdmin && !isAdmin && (
                  <Link to="/admin">
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Admin
                    </Button>
                  </Link>
                )}

                <div className="flex items-center space-x-3 px-3 py-2 bg-gray-50 rounded-lg">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{user?.firstName}</p>
                    <p className="text-xs text-gray-500">{user?.email}</p>
                  </div>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                  className="flex items-center space-x-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        defaultMode={authMode}
      />
    </header>
  );
};

export default Header;
