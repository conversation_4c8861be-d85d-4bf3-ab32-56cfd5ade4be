import { Phone, Clock, AlertTriangle, X } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON> } from './index';

const CancellationRestrictionModal = ({ isOpen, onClose, hoursRemaining }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Cancellation Restricted</h2>
                <p className="text-sm text-gray-600">Too close to booking time</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Time Restriction Info */}
          <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-5 w-5 text-orange-600" />
              <span className="font-medium text-orange-900">Time Restriction</span>
            </div>
            <p className="text-sm text-orange-800">
              Bookings can only be cancelled at least <strong>2 hours</strong> before the scheduled time.
            </p>
            <p className="text-sm text-orange-800 mt-1">
              Your booking is in <strong>{hoursRemaining.toFixed(1)} hours</strong>.
            </p>
          </div>

          {/* Contact Information */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-3">
              <Phone className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-900">Need to Cancel?</span>
            </div>
            <p className="text-sm text-blue-800 mb-3">
              Please call our support team for assistance with your cancellation:
            </p>
            
            <div className="bg-white p-3 rounded border border-blue-200">
              <div className="text-center">
                <div className="text-lg font-bold text-blue-900 mb-1">
                  📞 +92-300-1234567
                </div>
                <div className="text-sm text-blue-700">
                  Available 24/7
                </div>
              </div>
            </div>
            
            <p className="text-xs text-blue-700 mt-2 text-center">
              Our team will help you with cancellation and any applicable refunds
            </p>
          </div>

          {/* Action Button */}
          <div className="flex justify-center">
            <Button 
              onClick={onClose}
              className="px-8"
            >
              I Understand
            </Button>
          </div>

          {/* Additional Info */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-xs text-gray-600 text-center">
              <strong>Tip:</strong> For future bookings, cancel at least 2 hours in advance to use our online cancellation system.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CancellationRestrictionModal;
