import LoadingSpinner from "./LoadingSpinner";

const LoadingState = ({
  isLoading,
  children,
  text = "Loading...",
  spinnerSize = "md",
  fullScreen = false,
  overlay = false,
  className = "",
}) => {
  if (!isLoading) return children;

  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50">
        <div className="text-center">
          <LoadingSpinner
            size={spinnerSize}
            className="text-primary-600 mx-auto"
          />
          <p className="mt-4 text-secondary-700 font-medium">{text}</p>
        </div>
      </div>
    );
  }

  if (overlay) {
    return (
      <div className="relative">
        {children}
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10 rounded-lg">
          <div className="text-center">
            <LoadingSpinner
              size={spinnerSize}
              className="text-primary-600 mx-auto"
            />
            <p className="mt-2 text-secondary-700 font-medium text-sm">
              {text}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`flex flex-col items-center justify-center p-8 ${className}`}
    >
      <LoadingSpinner size={spinnerSize} className="text-primary-600 mx-auto" />
      <p className="mt-4 text-secondary-700 font-medium">{text}</p>
    </div>
  );
};

export default LoadingState;
