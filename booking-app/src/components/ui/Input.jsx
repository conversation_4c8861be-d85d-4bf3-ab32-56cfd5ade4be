import { clsx } from 'clsx';
import { forwardRef } from 'react';

const Input = forwardRef(({
  className = '',
  type = 'text',
  label,
  error,
  ...props
}, ref) => {
  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium leading-none text-dark-700">
          {label}
        </label>
      )}
      <input
        type={type}
        className={clsx(
          'flex h-10 w-full rounded-lg border border-dark-300 bg-light-50 px-3 py-2 text-sm text-dark-900 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-dark-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          error && 'border-error-500 focus-visible:ring-error-500',
          className
        )}
        ref={ref}
        {...props}
      />
      {error && (
        <p className="text-sm text-error-600">{error}</p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
