import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { X } from 'lucide-react';
import LoginForm from './LoginForm';
import RegisterForm from './RegisterForm';
import ForgotPasswordForm from './ForgotPasswordForm';

const AuthModal = ({ isOpen, onClose, defaultMode = 'login' }) => {
  const [mode, setMode] = useState(defaultMode); // 'login', 'register', 'forgot-password'
  const navigate = useNavigate();

  useEffect(() => {
    if (isOpen) {
      setMode(defaultMode);
    }
  }, [defaultMode, isOpen]);

  if (!isOpen) return null;

  const handleSuccess = (user) => {
    console.log('Authentication successful:', user);
    onClose();
    // Small delay to ensure state is updated, then navigate
    setTimeout(() => {
      navigate('/booking');
    }, 100);
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
      <div className="relative bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10 p-2"
        >
          <X size={20} />
        </button>
        
        <div className="p-6">
          {mode === 'login' ? (
            <LoginForm
              onSuccess={handleSuccess}
              onSwitchToRegister={() => setMode('register')}
              onSwitchToForgotPassword={() => setMode('forgot-password')}
            />
          ) : mode === 'register' ? (
            <RegisterForm
              onSuccess={handleSuccess}
              onSwitchToLogin={() => setMode('login')}
            />
          ) : (
            <ForgotPasswordForm
              onBack={() => setMode('login')}
              onSuccess={() => {
                setMode('login');
                // Show success message
                alert('Password reset successfully! Please login with your new password.');
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthModal;
