import { MapPin, Users, Star, Clock, Wifi, Car, Coffee } from 'lucide-react';
import { Card, Badge, Button } from '../ui';

const CourtDetails = ({ court, onBook, onClose }) => {
  if (!court) return null;

  const {
    id,
    name,
    description,
    image,
    images = [],
    capacity,
    rating,
    pricePerHour,
    amenities = [],
    location,
    rules = [],
    specifications = {}
  } = court;

  const amenityIcons = {
    'WiFi': Wifi,
    'Parking': Car,
    'Cafe': Coffee,
    // 'Showers': Shower,
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        {/* Image Gallery */}
        <div className="relative h-64 md:h-80 bg-secondary-100 rounded-t-xl overflow-hidden">
          {image ? (
            <img 
              src={image} 
              alt={name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <MapPin className="h-16 w-16 text-secondary-400" />
            </div>
          )}
          
          {/* Rating Overlay */}
          {rating && (
            <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 flex items-center space-x-2">
              <Star className="h-5 w-5 text-warning-500 fill-current" />
              <span className="font-medium text-secondary-900">{rating}</span>
            </div>
          )}
        </div>

        <Card.Content className="p-6">
          {/* Header */}
          <div className="flex justify-between items-start mb-6">
            <div>
              <h1 className="text-2xl font-bold text-secondary-900 mb-2">{name}</h1>
              {location && (
                <div className="flex items-center text-secondary-600 mb-2">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span>{location}</span>
                </div>
              )}
              <div className="flex items-center space-x-4 text-secondary-600">
                <div className="flex items-center space-x-1">
                  <Users className="h-4 w-4" />
                  <span>{capacity} players max</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span className="font-semibold text-primary-600">${pricePerHour}/hour</span>
                </div>
              </div>
            </div>
            <Button onClick={onBook} size="lg">
              Book This Court
            </Button>
          </div>

          {/* Description */}
          {description && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-secondary-900 mb-2">About</h3>
              <p className="text-secondary-700 leading-relaxed">{description}</p>
            </div>
          )}

          {/* Amenities */}
          {amenities.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">Amenities</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {amenities.map((amenity, index) => {
                  const Icon = amenityIcons[amenity] || Clock;
                  return (
                    <div key={index} className="flex items-center space-x-2 text-secondary-700">
                      <Icon className="h-4 w-4 text-primary-600" />
                      <span>{amenity}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Specifications */}
          {Object.keys(specifications).length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">Court Specifications</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {Object.entries(specifications).map(([key, value]) => (
                  <div key={key} className="bg-secondary-50 rounded-lg p-3">
                    <div className="text-sm text-secondary-600 capitalize">{key.replace(/([A-Z])/g, ' $1')}</div>
                    <div className="font-medium text-secondary-900">{value}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Rules */}
          {rules.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">Court Rules</h3>
              <ul className="space-y-2">
                {rules.map((rule, index) => (
                  <li key={index} className="flex items-start space-x-2 text-secondary-700">
                    <span className="text-primary-600 mt-1">•</span>
                    <span>{rule}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-4 pt-4 border-t border-secondary-200">
            <Button onClick={onBook} className="flex-1" size="lg">
              Book This Court
            </Button>
            {onClose && (
              <Button variant="outline" onClick={onClose} size="lg">
                Close
              </Button>
            )}
          </div>
        </Card.Content>
      </Card>
    </div>
  );
};

export default CourtDetails;
