import { Clock, MapPin, Users } from "lucide-react";
import { <PERSON>, But<PERSON> } from "../ui";

const CourtCard = ({ court, onSelect, isSelected = false }) => {
  const {
    name,
    description,
    capacity,
    pricePerHour,
  } = court;



  return (
    <Card
      className={`transition-all duration-200 hover:shadow-medium cursor-pointer ${
        isSelected ? "ring-2 ring-primary-500 shadow-medium" : ""
      }`}
    >
      <div onClick={() => onSelect?.(court)}>
        {/* Court Image */}
        <div className="relative h-48 bg-secondary-100 rounded-t-xl overflow-hidden">
          <div className="w-full h-full flex items-center justify-center">
            <MapPin className="h-12 w-12 text-secondary-400" />
          </div>
        </div>

        <Card.Content className="p-4">
          {/* Court Info */}
          <div className="mb-3">
            <h3 className="text-lg font-semibold text-secondary-900 mb-1">
              {name}
            </h3>
            {description && (
              <p className="text-sm text-secondary-600 line-clamp-2">
                {description}
              </p>
            )}
          </div>

          {/* Court Details */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4 text-sm text-secondary-600">
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{capacity} players</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>Rs. {pricePerHour}/hr</span>
              </div>
            </div>
          </div>



          {/* Action Button */}
          <Button
            variant={isSelected ? "primary" : "outline"}
            className="w-full"
          >
            {"Select Court"}
          </Button>
        </Card.Content>
      </div>
    </Card>
  );
};

export default CourtCard;
