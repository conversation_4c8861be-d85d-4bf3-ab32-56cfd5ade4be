import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { User, Mail, Calendar, Clock, MapPin } from 'lucide-react';
import { Card, Input, Button, Select, Badge } from '../ui';
import { useAuth } from '../../hooks/useAuth';

const BookingForm = ({
  court,
  selectedDate,
  selectedSlot,
  onSubmit,
  onCancel,
  loading = false,
  error = ''
}) => {
  const { user, isAuthenticated } = useAuth();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '+92',
    paymentMethod: 'cash'
  });

  // Prefill form with user data if authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      setFormData(prev => ({
        ...prev,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phone: user.phone || '+92'
      }));
    }
  }, [isAuthenticated, user]);

  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    if (field === 'phone') {
      // Ensure phone always starts with +92
      let phoneValue = value;
      if (!phoneValue.startsWith('+92')) {
        phoneValue = '+92' + phoneValue.replace(/^\+?92?/, '');
      }
      // Limit to +92 + 10 digits
      if (phoneValue.length > 13) {
        phoneValue = phoneValue.substring(0, 13);
      }
      setFormData(prev => ({ ...prev, [field]: phoneValue }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';
    if (!formData.phone.trim() || formData.phone === '+92') {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+92\d{10}$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid Pakistani phone number (+92 followed by 10 digits)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit({
        ...formData,
        court,
        date: selectedDate,
        timeSlot: selectedSlot,
        totalPrice: selectedSlot.price
      });
    }
  };

  if (!court || !selectedDate || !selectedSlot) {
    return (
      <Card>
        <div className="p-6 text-center">
          <p className="text-secondary-600">Please select a court, date, and time slot to continue.</p>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="p-6">
        {/* Booking Summary */}
        <div className="mb-6 p-4 bg-primary-50 rounded-lg">
          <h3 className="text-lg font-semibold text-primary-900 mb-3">Booking Summary</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-primary-600" />
              <span className="font-medium">{court.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-primary-600" />
              <span>{format(selectedDate, 'EEEE, MMMM d, yyyy')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-primary-600" />
              <span>{selectedSlot.startTime} - {selectedSlot.endTime}</span>
            </div>
            <div className="flex items-center justify-between pt-2 border-t border-primary-200">
              <span className="font-medium">Total Price:</span>
              <Badge variant="primary" size="lg">Rs. {selectedSlot.price}</Badge>
            </div>
          </div>
        </div>

        {/* Booking Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Personal Information */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3 flex items-center">
              <User className="h-4 w-4 mr-2" />
              Personal Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="First Name"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                error={errors.firstName}
                required
                disabled={isAuthenticated}
              />
              <Input
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                error={errors.lastName}
                required
                disabled={isAuthenticated}
              />
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3 flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              Contact Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={errors.email}
                required
                disabled={isAuthenticated}
              />
              <Input
                label="Phone Number"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                error={errors.phone}
                placeholder="+92XXXXXXXXXX"
                required
                disabled={isAuthenticated}
              />
            </div>
          </div>

          {/* Payment Details */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3">Payment Method</h4>
            <Select
              label="Payment Method"
              value={formData.paymentMethod}
              onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
            >
              <option value="cash">Pay at Venue</option>
              <option value="card" disabled>Credit/Debit Card (Coming Soon)</option>
            </Select>
          </div>

          {/* Terms and Conditions */}
          <div className="p-4 bg-secondary-50 rounded-lg">
            <p className="text-sm text-secondary-700">
              By booking this court, you agree to our terms and conditions. 
              Cancellations must be made at least 2 hours before the booking time.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4 pt-4">
            <Button
              type="submit"
              className="flex-1"
              disabled={loading}
              size="lg"
            >
              {loading ? 'Processing...' : `Book Court - Rs. ${selectedSlot.price}`}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
              size="lg"
            >
              Cancel
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600 font-medium">{error}</p>
            </div>
          )}
        </form>
      </div>
    </Card>
  );
};

export default BookingForm;
