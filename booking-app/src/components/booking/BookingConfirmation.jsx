import { format } from 'date-fns';
import { CheckCircle, Calendar, Clock, MapPin, User, Mail, Phone } from 'lucide-react';
import { Card, Button, Badge } from '../ui';

// Utility function to parse date strings as local dates
const parseLocalDate = (dateString) => {
  if (dateString.includes('T')) {
    return new Date(dateString);
  }
  const [year, month, day] = dateString.split('-').map(Number);
  return new Date(year, month - 1, day);
};

const BookingConfirmation = ({ booking, onClose }) => {
  if (!booking) return null;

  const {
    court,
    date,
    timeSlot,
    customer,
    totalPrice,
    status = 'pending'
  } = booking;

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <div className="p-6">
          {/* Success Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-8 w-8 text-success-600" />
            </div>
            <h2 className="text-2xl font-bold text-secondary-900 mb-2">Booking Confirmed!</h2>
            <p className="text-secondary-600">
              Your court has been successfully booked. You'll receive a confirmation email shortly.
            </p>
          </div>

          {/* Booking Details */}
          <div className="space-y-6">

            {/* Court and Time Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">{court.name}</div>
                    {court.location && (
                      <div className="text-sm text-secondary-600">{court.location}</div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">
                      {format(parseLocalDate(date), 'EEEE, MMMM d, yyyy')}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">
                      {timeSlot.startTime} - {timeSlot.endTime}
                    </div>
                    <div className="text-sm text-secondary-600">
                      Duration: {timeSlot.duration || '1 hour'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">
                      {customer?.firstName} {customer?.lastName}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">{customer?.email}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">{customer?.phone}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Details */}
            <div className="p-4 bg-secondary-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-secondary-900">Booking Details</span>
                <Badge variant="warning">Pending</Badge>
              </div>
              <div className="flex items-center justify-between text-sm text-secondary-600 mb-1">
                <span>Court booking</span>
                <span>Rs. {totalPrice}</span>
              </div>
              <div className="flex items-center justify-between text-sm text-secondary-600 mb-2">
                <span>Payment method</span>
                <span>Pay at venue</span>
              </div>
              <div className="flex items-center justify-between font-medium text-secondary-900 pt-2 border-t border-secondary-200">
                <span>Total</span>
                <span>Rs. {totalPrice}</span>
              </div>
            </div>



            {/* Important Information */}
            <div className="p-4 bg-primary-50 rounded-lg">
              <h4 className="font-medium text-primary-900 mb-2">Important Information</h4>
              <ul className="text-sm text-primary-800 space-y-1">
                <li>• Please arrive 10 minutes before your booking time</li>
                <li>• Bring appropriate sports attire and equipment</li>
                <li>• Cancellations must be made at least 2 hours in advance</li>
                <li>• Contact us if you need to reschedule your booking</li>
              </ul>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center mt-6 pt-6 border-t border-secondary-200">
            <Button
              onClick={onClose}
              className="px-8"
            >
              Done
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default BookingConfirmation;
