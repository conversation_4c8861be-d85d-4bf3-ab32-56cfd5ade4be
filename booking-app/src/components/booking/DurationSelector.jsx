import { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

const DurationSelector = ({ selectedDuration, onDurationChange, className = '' }) => {
  const [isCustom, setIsCustom] = useState(false);
  const [customHours, setCustomHours] = useState(1);
  const [customMinutes, setCustomMinutes] = useState(0);

  const predefinedDurations = [
    { label: '1 Hour', value: 60 },
    { label: '1.5 Hours', value: 90 },
    { label: '2 Hours', value: 120 },
    { label: '2.5 Hours', value: 150 },
    { label: '3 Hours', value: 180 },
  ];

  const handlePredefinedSelect = (duration) => {
    setIsCustom(false);
    onDurationChange(duration);
  };

  const handleCustomToggle = () => {
    if (!isCustom) {
      setIsCustom(true);
      // Convert current duration to hours and minutes
      const hours = Math.floor(selectedDuration / 60);
      const minutes = selectedDuration % 60;
      setCustomHours(hours);
      setCustomMinutes(minutes);
    } else {
      setIsCustom(false);
    }
  };

  const handleCustomChange = () => {
    const totalMinutes = customHours * 60 + customMinutes;
    if (totalMinutes >= 30 && totalMinutes <= 480) {
      onDurationChange(totalMinutes);
    }
  };

  // Update custom duration when it changes
  useEffect(() => {
    if (isCustom) {
      handleCustomChange();
    }
  }, [customHours, customMinutes, isCustom]);

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours === 0) return `${mins} min`;
    if (mins === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return `${hours}h ${mins}m`;
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Clock className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Booking Duration</h3>
      </div>

      <div className="space-y-3">
        {/* Predefined durations */}
        <div className="grid grid-cols-2 gap-2">
          {predefinedDurations.map((duration) => (
            <button
              key={duration.value}
              onClick={() => handlePredefinedSelect(duration.value)}
              className={`p-3 text-sm font-medium rounded-lg border transition-colors ${
                selectedDuration === duration.value && !isCustom
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'
              }`}
            >
              {duration.label}
            </button>
          ))}
        </div>

        {/* Custom duration */}
        <div className="border-t pt-3">
          <button
            onClick={handleCustomToggle}
            className={`w-full p-3 text-sm font-medium rounded-lg border transition-colors ${
              isCustom
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'
            }`}
          >
            Custom Duration
          </button>

          {isCustom && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="flex-1">
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Hours
                  </label>
                  <select
                    value={customHours}
                    onChange={(e) => setCustomHours(parseInt(e.target.value))}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {Array.from({ length: 9 }, (_, i) => (
                      <option key={i} value={i}>{i}</option>
                    ))}
                  </select>
                </div>
                <div className="flex-1">
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Minutes
                  </label>
                  <select
                    value={customMinutes}
                    onChange={(e) => setCustomMinutes(parseInt(e.target.value))}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={0}>0</option>
                    <option value={15}>15</option>
                    <option value={30}>30</option>
                    <option value={45}>45</option>
                  </select>
                </div>
              </div>
              <p className="text-xs text-gray-600">
                Minimum: 30 minutes, Maximum: 8 hours
              </p>
            </div>
          )}
        </div>

        {/* Selected duration display */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-blue-900">
              Selected Duration:
            </span>
            <span className="text-lg font-bold text-blue-600">
              {formatDuration(selectedDuration)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DurationSelector;
