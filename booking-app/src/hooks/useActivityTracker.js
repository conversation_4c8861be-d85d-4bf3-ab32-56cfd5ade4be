import { useEffect, useRef, useCallback } from 'react';
import { useAuth } from './useAuth';

const useActivityTracker = () => {
  const { isAuthenticated, user } = useAuth();
  const sessionRef = useRef(null);
  const activityRef = useRef([]);
  const timeoutRef = useRef(null);

  // Initialize session
  const initializeSession = useCallback(() => {
    if (!isAuthenticated) return;

    sessionRef.current = {
      sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: user?.id,
      startTime: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer,
      screenResolution: `${screen.width}x${screen.height}`,
      viewport: `${window.innerWidth}x${window.innerHeight}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language
    };

    activityRef.current = [];
    console.log('Activity tracking session started:', sessionRef.current.sessionId);
  }, [isAuthenticated, user]);

  // Track activity
  const trackActivity = useCallback((action, data = {}) => {
    if (!sessionRef.current) return;

    const activity = {
      timestamp: new Date().toISOString(),
      action,
      data,
      url: window.location.href,
      sessionId: sessionRef.current.sessionId
    };

    activityRef.current.push(activity);
    console.log('Activity tracked:', activity);

    // Auto-save every 10 activities or after 30 seconds of inactivity
    if (activityRef.current.length >= 10) {
      saveActivities();
    } else {
      // Reset timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(saveActivities, 30000);
    }
  }, []);

  // Save activities to backend
  const saveActivities = useCallback(async () => {
    if (!sessionRef.current || activityRef.current.length === 0) return;

    try {
      const payload = {
        session: sessionRef.current,
        activities: [...activityRef.current]
      };

      // Send to backend (implement this endpoint)
      await fetch('/api/v1/analytics/track-activity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify(payload)
      });

      // Clear saved activities
      activityRef.current = [];
      console.log('Activities saved to backend');
    } catch (error) {
      console.error('Failed to save activities:', error);
    }
  }, []);

  // Track booking flow specifically
  const trackBookingFlow = useCallback((step, data = {}) => {
    trackActivity('booking_flow', {
      step,
      ...data,
      flowTimestamp: new Date().toISOString()
    });
  }, [trackActivity]);

  // Track abandonment
  const trackAbandonment = useCallback((reason, data = {}) => {
    trackActivity('booking_abandonment', {
      reason,
      ...data,
      abandonmentTimestamp: new Date().toISOString(),
      timeSpent: sessionRef.current ? 
        (new Date() - new Date(sessionRef.current.startTime)) / 1000 : 0
    });
    
    // Immediately save abandonment data
    saveActivities();
  }, [trackActivity, saveActivities]);

  // Track page views
  const trackPageView = useCallback((page, data = {}) => {
    trackActivity('page_view', {
      page,
      ...data
    });
  }, [trackActivity]);

  // Track form interactions
  const trackFormInteraction = useCallback((formName, field, action, value = null) => {
    trackActivity('form_interaction', {
      formName,
      field,
      action, // 'focus', 'blur', 'change', 'submit'
      value: action === 'change' ? value : undefined
    });
  }, [trackActivity]);

  // Track errors
  const trackError = useCallback((error, context = {}) => {
    trackActivity('error', {
      message: error.message,
      stack: error.stack,
      context
    });
  }, [trackActivity]);

  // Initialize session on mount
  useEffect(() => {
    initializeSession();
  }, [initializeSession]);

  // Save activities before page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (activityRef.current.length > 0) {
        // Use sendBeacon for reliable data sending on page unload
        const payload = {
          session: sessionRef.current,
          activities: activityRef.current
        };
        
        navigator.sendBeacon(
          '/api/v1/analytics/track-activity',
          JSON.stringify(payload)
        );
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Track route changes
  useEffect(() => {
    trackPageView(window.location.pathname);
  }, [window.location.pathname, trackPageView]);

  return {
    trackActivity,
    trackBookingFlow,
    trackAbandonment,
    trackPageView,
    trackFormInteraction,
    trackError,
    sessionId: sessionRef.current?.sessionId
  };
};

export default useActivityTracker;
