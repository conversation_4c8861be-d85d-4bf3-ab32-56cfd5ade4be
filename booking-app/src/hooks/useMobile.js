import { useState, useEffect } from 'react';

export const useMobile = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
      setIsDesktop(width >= 1024);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);

    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  return { isMobile, isTablet, isDesktop };
};

export const useCapacitor = () => {
  const [isCapacitor, setIsCapacitor] = useState(false);
  const [platform, setPlatform] = useState('web');

  useEffect(() => {
    // Check if running in Capacitor
    const isCapacitorApp = window.Capacitor?.isNativePlatform?.() || false;
    setIsCapacitor(isCapacitorApp);

    if (isCapacitorApp) {
      // Get platform info
      import('@capacitor/device').then(({ Device }) => {
        Device.getInfo().then((info) => {
          setPlatform(info.platform);
        });
      }).catch(() => {
        // Fallback if Capacitor Device plugin not available
        setPlatform('unknown');
      });
    }
  }, []);

  return { isCapacitor, platform };
};
