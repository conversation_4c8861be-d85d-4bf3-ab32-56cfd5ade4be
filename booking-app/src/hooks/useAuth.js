import { useApp } from '../context/AppContext';

export const useAuth = () => {
  const { state, actions } = useApp();

  return {
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    loading: state.loading,
    error: state.error,
    login: actions.login,
    register: actions.register,
    logout: actions.logout,
    isAdmin: state.user?.role === 'admin',
    isStaff: state.user?.role === 'staff' || state.user?.role === 'admin'
  };
};

export default useAuth;
