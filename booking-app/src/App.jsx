import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AppProvider } from './context/AppContext';
import { ErrorBoundary } from './components/ui';
import Layout from './components/layout/Layout';
import AdminLayout from './components/layout/AdminLayout';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Public Pages
import LandingPage from './pages/LandingPage';
import AboutUs from './pages/AboutUs';
import ContactUs from './pages/ContactUs';
import TermsAndConditions from './pages/TermsAndConditions';
import PrivacyPolicy from './pages/PrivacyPolicy';
import RefundPolicy from './pages/RefundPolicy';
import CookiePolicy from './pages/CookiePolicy';

// User Pages (Protected)
import BookingPage from './pages/BookingPage';
import MyBookingsPage from './pages/MyBookingsPage';
import UserSettings from './pages/UserSettings';

// Admin Pages
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminBookings from './pages/admin/AdminBookings';
import AdminCourts from './pages/admin/AdminCourts';
import AdminSchedule from './pages/admin/AdminSchedule';
import AdminSettings from './pages/admin/AdminSettings';

function App() {
  return (
    <ErrorBoundary>
      <AppProvider>
        <Router>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={
              <Layout>
                <LandingPage />
              </Layout>
            } />
            <Route path="/about" element={
              <Layout>
                <AboutUs />
              </Layout>
            } />
            <Route path="/contact" element={
              <Layout>
                <ContactUs />
              </Layout>
            } />
            <Route path="/terms" element={
              <Layout>
                <TermsAndConditions />
              </Layout>
            } />
            <Route path="/privacy" element={
              <Layout>
                <PrivacyPolicy />
              </Layout>
            } />
            <Route path="/refund-policy" element={
              <Layout>
                <RefundPolicy />
              </Layout>
            } />
            <Route path="/cookie-policy" element={
              <Layout>
                <CookiePolicy />
              </Layout>
            } />

            {/* Protected User Routes */}
            <Route path="/booking" element={
              <ProtectedRoute>
                <Layout>
                  <BookingPage />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path="/my-bookings" element={
              <ProtectedRoute>
                <Layout>
                  <MyBookingsPage />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path="/settings" element={
              <ProtectedRoute>
                <Layout>
                  <UserSettings />
                </Layout>
              </ProtectedRoute>
            } />

            {/* Admin Routes */}
            <Route path="/admin" element={
              <AdminLayout>
                <AdminDashboard />
              </AdminLayout>
            } />
            <Route path="/admin/bookings" element={
              <AdminLayout>
                <AdminBookings />
              </AdminLayout>
            } />
            <Route path="/admin/courts" element={
              <AdminLayout>
                <AdminCourts />
              </AdminLayout>
            } />
            <Route path="/admin/schedule" element={
              <AdminLayout>
                <AdminSchedule />
              </AdminLayout>
            } />
            <Route path="/admin/settings" element={
              <AdminLayout>
                <AdminSettings />
              </AdminLayout>
            } />
          </Routes>
        </Router>
      </AppProvider>
    </ErrorBoundary>
  );
}

export default App;
