import { createContext, useContext, useReducer, useEffect } from 'react';
import { authService, courtService, bookingService, operatingHoursService } from '../services/index.js';

// Initial state
const initialState = {
  courts: [],
  bookings: [],
  operatingHours: {},
  user: null,
  loading: false,
  error: null,
  isAuthenticated: false,
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_USER: 'SET_USER',
  SET_AUTHENTICATED: 'SET_AUTHENTICATED',

  // Courts
  SET_COURTS: 'SET_COURTS',
  ADD_COURT: 'ADD_COURT',
  UPDATE_COURT: 'UPDATE_COURT',
  DELETE_COURT: 'DELETE_COURT',

  // Bookings
  SET_BOOKINGS: 'SET_BOOKINGS',
  ADD_BOOKING: 'ADD_BOOKING',
  UPDATE_BOOKING: 'UPDATE_BOOKING',
  DELETE_BOOKING: 'DELETE_BOOKING',

  // Operating Hours
  SET_OPERATING_HOURS: 'SET_OPERATING_HOURS',
};

// Reducer function
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };

    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };

    case ActionTypes.SET_USER:
      return { ...state, user: action.payload };

    case ActionTypes.SET_AUTHENTICATED:
      return { ...state, isAuthenticated: action.payload };
      
    // Courts
    case ActionTypes.SET_COURTS:
      return { ...state, courts: action.payload };
      
    case ActionTypes.ADD_COURT:
      return { ...state, courts: [...state.courts, action.payload] };
      
    case ActionTypes.UPDATE_COURT:
      return {
        ...state,
        courts: state.courts.map(court =>
          court.id === action.payload.id ? { ...court, ...action.payload } : court
        )
      };
      
    case ActionTypes.DELETE_COURT:
      return {
        ...state,
        courts: state.courts.filter(court => court.id !== action.payload)
      };
      
    // Bookings
    case ActionTypes.SET_BOOKINGS:
      return { ...state, bookings: action.payload };
      
    case ActionTypes.ADD_BOOKING:
      return { ...state, bookings: [...state.bookings, action.payload] };
      
    case ActionTypes.UPDATE_BOOKING:
      return {
        ...state,
        bookings: state.bookings.map(booking =>
          booking.id === action.payload.id ? { ...booking, ...action.payload } : booking
        )
      };
      
    case ActionTypes.DELETE_BOOKING:
      return {
        ...state,
        bookings: state.bookings.filter(booking => booking.id !== action.payload)
      };
      
    // Operating Hours
    case ActionTypes.SET_OPERATING_HOURS:
      return { ...state, operatingHours: action.payload };
    default:
      return state;
  }
};

// Create context
const AppContext = createContext();

// Context provider component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Action creators
  const actions = {
    setLoading: (loading) => dispatch({ type: ActionTypes.SET_LOADING, payload: loading }),
    setError: (error) => dispatch({ type: ActionTypes.SET_ERROR, payload: error }),
    setUser: (user) => dispatch({ type: ActionTypes.SET_USER, payload: user }),
    setAuthenticated: (isAuth) => dispatch({ type: ActionTypes.SET_AUTHENTICATED, payload: isAuth }),

    // Courts
    setCourts: (courts) => dispatch({ type: ActionTypes.SET_COURTS, payload: courts }),
    addCourt: (court) => dispatch({ type: ActionTypes.ADD_COURT, payload: court }),
    updateCourt: (court) => dispatch({ type: ActionTypes.UPDATE_COURT, payload: court }),
    deleteCourt: (courtId) => dispatch({ type: ActionTypes.DELETE_COURT, payload: courtId }),

    // Bookings
    setBookings: (bookings) => dispatch({ type: ActionTypes.SET_BOOKINGS, payload: bookings }),
    addBooking: (booking) => dispatch({ type: ActionTypes.ADD_BOOKING, payload: booking }),
    updateBooking: (booking) => dispatch({ type: ActionTypes.UPDATE_BOOKING, payload: booking }),
    deleteBooking: (bookingId) => dispatch({ type: ActionTypes.DELETE_BOOKING, payload: bookingId }),

    // Operating Hours
    setOperatingHours: (hours) => dispatch({ type: ActionTypes.SET_OPERATING_HOURS, payload: hours }),
    
    // API Actions
    // Authentication
    login: async (email, password) => {
      actions.setLoading(true);
      actions.setError(null);

      const { user, error } = await authService.login(email, password);

      if (error) {
        actions.setError(error.message);
        actions.setLoading(false);
        return { success: false, error };
      }

      actions.setUser(user);
      actions.setAuthenticated(true);
      actions.setLoading(false);
      return { success: true, user };
    },

    register: async (userData) => {
      actions.setLoading(true);
      actions.setError(null);

      const { user, error } = await authService.register(userData);

      if (error) {
        actions.setError(error.message);
        actions.setLoading(false);
        return { success: false, error };
      }

      actions.setUser(user);
      actions.setAuthenticated(true);
      actions.setLoading(false);
      return { success: true, user };
    },

    logout: async () => {
      actions.setLoading(true);
      await authService.logout();
      actions.setUser(null);
      actions.setAuthenticated(false);
      actions.setBookings([]);
      actions.setLoading(false);
    },

    updateUser: (updatedUser) => {
      actions.setUser(updatedUser);
    },

    // Courts
    fetchCourts: async (filters = {}) => {
      actions.setLoading(true);
      actions.setError(null);

      const { courts, error } = await courtService.getCourts(filters);

      if (error) {
        actions.setError(error.message);
        actions.setLoading(false);
        return { success: false, error };
      }

      actions.setCourts(courts);
      actions.setLoading(false);
      return { success: true, courts };
    },

    // Bookings
    fetchUserBookings: async (filters = {}) => {
      actions.setLoading(true);
      actions.setError(null);

      const { bookings, error } = await bookingService.getUserBookings(filters);

      if (error) {
        actions.setError(error.message);
        actions.setLoading(false);
        return { success: false, error };
      }

      actions.setBookings(bookings);
      actions.setLoading(false);
      return { success: true, bookings };
    },

    createBooking: async (bookingData) => {
      actions.setLoading(true);
      actions.setError(null);

      const { booking, error } = await bookingService.createBooking(bookingData);

      if (error) {
        actions.setError(error.message);
        actions.setLoading(false);
        return { success: false, error };
      }

      actions.addBooking(booking);
      actions.setLoading(false);
      return { success: true, booking };
    },

    // Operating Hours
    fetchOperatingHours: async () => {
      const { schedule, error } = await operatingHoursService.getOperatingHours();

      if (error) {
        actions.setError(error.message);
        return { success: false, error };
      }

      actions.setOperatingHours(schedule);
      return { success: true, schedule };
    }
  };

  // Initialize authentication and load initial data
  useEffect(() => {
    const initializeApp = async () => {
      actions.setLoading(true);

      // Check if user is authenticated
      if (authService.isAuthenticated()) {
        try {
          const { user, error } = await authService.getCurrentUser();
          if (user && !error) {
            actions.setUser(user);
            actions.setAuthenticated(true);
            // Load user's bookings if authenticated
            actions.fetchUserBookings();
          } else {
            actions.setAuthenticated(false);
          }
        } catch (error) {
          console.error('Error loading user:', error);
          actions.setAuthenticated(false);
        }
      }

      // Load public data (courts, operating hours)
      try {
        await Promise.all([
          actions.fetchCourts(),
          actions.fetchOperatingHours()
        ]);
      } catch (error) {
        console.error('Error loading initial data:', error);
      }

      actions.setLoading(false);
    };

    initializeApp();
  }, []);

  const value = {
    state,
    actions
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
