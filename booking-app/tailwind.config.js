/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      colors: {
        // Primary color palette
        primary: {
          50: '#FEF3F0',
          100: '#FDE6E0',
          200: '#FBCDC1',
          300: '#F8B4A2',
          400: '#F59B83',
          500: '#F28264',
          600: '#ED5E12', // Main primary color
          700: '#D4520F',
          800: '#BB460D',
          900: '#A23A0B',
          950: '#892E09',
        },
        // Secondary color palette
        secondary: {
          50: '#FEFCF0',
          100: '#FDF8E0',
          200: '#FBF1C1',
          300: '#F8EAA2',
          400: '#F5E383',
          500: '#F2DC64',
          600: '#EDB112', // Main secondary color
          700: '#D49E0F',
          800: '#BB8B0D',
          900: '#A2780B',
          950: '#896509',
        },
        // Dark color (replaces black)
        dark: {
          50: '#F7F7F7',
          100: '#EFEFEF',
          200: '#DFDFDF',
          300: '#CFCFCF',
          400: '#BFBFBF',
          500: '#AFAFAF',
          600: '#9F9F9F',
          700: '#8F8F8F',
          800: '#7F7F7F',
          900: '#2C2C2C', // Main dark color
          950: '#1A1A1A',
        },
        // Light color (replaces white)
        light: {
          50: '#FFFFFF',
          100: '#FAFAFA', // Main light color
          200: '#F5F5F5', // Light gray
          300: '#F0F0F0',
          400: '#EBEBEB',
          500: '#E6E6E6',
          600: '#E1E1E1',
          700: '#DCDCDC',
          800: '#D7D7D7',
          900: '#D2D2D2',
          950: '#CDCDCD',
        },
        // Semantic colors using the new palette
        success: {
          50: '#F0FDF4',
          100: '#DCFCE7',
          200: '#BBF7D0',
          300: '#86EFAC',
          400: '#4ADE80',
          500: '#22C55E',
          600: '#16A34A',
          700: '#15803D',
          800: '#166534',
          900: '#14532D',
        },
        warning: {
          50: '#FEFCF0',
          100: '#FDF8E0',
          200: '#FBF1C1',
          300: '#F8EAA2',
          400: '#F5E383',
          500: '#F2DC64',
          600: '#EDB112', // Uses secondary color
          700: '#D49E0F',
          800: '#BB8B0D',
          900: '#A2780B',
        },
        error: {
          50: '#FEF2F2',
          100: '#FEE2E2',
          200: '#FECACA',
          300: '#FCA5A5',
          400: '#F87171',
          500: '#EF4444',
          600: '#DC2626',
          700: '#B91C1C',
          800: '#991B1B',
          900: '#7F1D1D',
        },
        info: {
          50: '#EFF6FF',
          100: '#DBEAFE',
          200: '#BFDBFE',
          300: '#93C5FD',
          400: '#60A5FA',
          500: '#3B82F6',
          600: '#2563EB',
          700: '#1D4ED8',
          800: '#1E40AF',
          900: '#1E3A8A',
        },
      },
    },
  },
  plugins: [],
}
