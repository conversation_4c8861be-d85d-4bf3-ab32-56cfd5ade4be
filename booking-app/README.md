# Padel Chase - Padel Tennis Court Booking App

A modern, responsive web application for booking padel tennis courts built with React, Vite, and Tailwind CSS.

## Features

### User Features
- **Court Selection**: Browse and filter available courts with detailed information
- **Date & Time Booking**: Interactive calendar and time slot selection
- **Booking Management**: View, manage, and cancel bookings
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Modern UI/UX**: Beautiful, intuitive interface with smooth animations

### Admin Features
- **Dashboard**: Overview of bookings, revenue, and court utilization
- **Booking Management**: View, filter, and manage all bookings
- **Court Management**: Add, edit, and manage court information
- **Schedule Management**: Set operating hours, holidays, and court availability
- **Settings**: Configure business information and booking policies

## Tech Stack

- **Frontend**: React 19, Vite
- **Styling**: Tailwind CSS with custom design system
- **Routing**: React Router DOM
- **Icons**: Lucide React
- **Date Handling**: date-fns
- **State Management**: React Context API
- **Build Tool**: Vite

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components (Button, Card, etc.)
│   ├── courts/         # Court-related components
│   ├── booking/        # Booking-related components
│   └── layout/         # Layout components
├── pages/              # Page components
│   ├── admin/          # Admin panel pages
│   └── ...             # User pages
├── context/            # React Context for state management
├── data/               # Mock data and data utilities
├── utils/              # Utility functions
└── ...
```

## Features Overview

### Booking Flow
1. **Select Court**: Filter and choose from available courts
2. **Pick Date & Time**: Use the calendar and time slot grid
3. **Enter Details**: Fill in booking information
4. **Confirm**: Review and confirm the booking

### Admin Panel
- **Dashboard**: Key metrics and recent activity
- **Bookings**: Comprehensive booking management
- **Courts**: Court configuration and management
- **Schedule**: Operating hours and availability settings
- **Settings**: Business and system configuration

## Design System

The app uses a custom design system built with Tailwind CSS featuring:
- **Color Palette**: Primary, secondary, success, warning, and error colors
- **Typography**: Inter font family with consistent sizing
- **Components**: Reusable UI components with consistent styling
- **Animations**: Smooth transitions and micro-interactions
- **Responsive**: Mobile-first responsive design

## Mock Data

The application includes comprehensive mock data for demonstration:
- **Courts**: 4 different court types with specifications
- **Bookings**: Generated bookings for past and future dates
- **Operating Hours**: Configurable daily schedules
- **Holidays**: Special dates and closures

## State Management

Uses React Context API for:
- **Courts**: Court data and management
- **Bookings**: Booking data and operations
- **Filters**: Search and filter states
- **User**: User authentication and preferences

## Responsive Design

- **Mobile**: Optimized for phones with touch-friendly interface
- **Tablet**: Adapted layouts for medium screens
- **Desktop**: Full-featured interface with sidebar navigation

## Future Enhancements

This is a UI-only implementation. Future backend integration would include:
- **Authentication**: User login and registration
- **Payment Processing**: Secure payment handling
- **Real-time Updates**: Live booking status updates
- **Email Notifications**: Booking confirmations and reminders
- **API Integration**: RESTful API for data management
- **Database**: Persistent data storage

## License

This project is licensed under the MIT License.
