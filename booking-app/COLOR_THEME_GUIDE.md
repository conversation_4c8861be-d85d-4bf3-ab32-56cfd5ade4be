# Padel Chase Color Theme Guide

## 🎨 New Color Palette

### Primary Colors
- **Primary**: `#ED5E12` (Orange-red)
  - Used for: Main CTAs, active states, primary buttons, focus states
  - Tailwind classes: `bg-primary-600`, `text-primary-600`, `border-primary-600`

### Secondary Colors  
- **Secondary**: `#EDB112` (Golden yellow)
  - Used for: Secondary buttons, highlights, warning states
  - Tailwind classes: `bg-secondary-600`, `text-secondary-600`, `border-secondary-600`

### Neutral Colors
- **Dark**: `#2C2C2C` (Replaces black)
  - Used for: Text, dark backgrounds, borders
  - Tailwind classes: `bg-dark-900`, `text-dark-900`, `border-dark-900`

- **Light**: `#FAFAFA` (Replaces white)
  - Used for: Backgrounds, cards, light text
  - Tailwind classes: `bg-light-100`, `text-light-100`, `border-light-100`

- **Gray**: `#F5F5F5` (Light gray)
  - Used for: Subtle backgrounds, dividers
  - Tailwind classes: `bg-light-200`, `text-light-200`, `border-light-200`

## 🛠️ Implementation

### 1. Tailwind Configuration
The color palette is defined in `tailwind.config.js` with full shade ranges (50-950) for each color.

### 2. CSS Variables
CSS custom properties are available in `index.css` for direct usage:
```css
:root {
  --color-primary-600: #ED5E12;
  --color-secondary-600: #EDB112;
  --color-dark-900: #2C2C2C;
  --color-light-100: #FAFAFA;
  --color-light-200: #F5F5F5;
}
```

### 3. Component Classes
Pre-built component classes are available:
```css
.btn-primary { @apply bg-primary-600 text-light-50 hover:bg-primary-700; }
.btn-secondary { @apply bg-secondary-600 text-dark-900 hover:bg-secondary-700; }
.card { @apply bg-light-50 border border-light-300 shadow-sm; }
.input { @apply border border-dark-300 bg-light-50 text-dark-900; }
```

## 📋 Usage Guidelines

### Do's ✅
- Use semantic color names: `primary`, `secondary`, `dark`, `light`
- Use appropriate shade levels (50-950)
- Maintain consistent contrast ratios
- Use `primary` for main actions and CTAs
- Use `secondary` for supporting actions
- Use `dark` for text and important elements
- Use `light` for backgrounds and cards

### Don'ts ❌
- Don't use hardcoded hex values in components
- Don't use `black`, `white`, `gray` classes
- Don't use `blue`, `green`, `red` for brand elements
- Don't mix old and new color systems

## 🎯 Common Patterns

### Buttons
```jsx
<Button variant="primary">Primary Action</Button>
<Button variant="secondary">Secondary Action</Button>
<Button variant="outline">Outline Button</Button>
<Button variant="ghost">Ghost Button</Button>
```

### Text Colors
```jsx
<h1 className="text-dark-900">Main Heading</h1>
<p className="text-dark-700">Body Text</p>
<span className="text-dark-500">Muted Text</span>
<a className="text-primary-600">Link</a>
```

### Backgrounds
```jsx
<div className="bg-light-100">Page Background</div>
<div className="bg-light-50">Card Background</div>
<div className="bg-light-200">Subtle Background</div>
<div className="bg-primary-600">Primary Background</div>
```

### States
```jsx
<div className="hover:bg-light-200">Hover State</div>
<div className="focus:ring-primary-500">Focus State</div>
<div className="active:bg-primary-800">Active State</div>
```

## 🔧 Migration Notes

### Updated Components
- ✅ Button: All variants updated
- ✅ Card: Background and borders
- ✅ Input: Colors and focus states
- ✅ Modal: Background and backdrop
- ✅ Badge: All variants updated
- ✅ Table: Headers, rows, and cells
- ✅ Header: Background and user avatar
- ✅ Footer: Background and text colors
- ✅ Layout: Page backgrounds
- ✅ DatePicker: Selection and hover states
- ✅ BookingPage: Progress indicators
- ✅ AdminLayout: Sidebar and backgrounds

### Semantic Colors
The theme includes semantic colors that map to the new palette:
- `success`: Green shades for success states
- `warning`: Uses secondary color for warnings
- `error`: Red shades for error states
- `info`: Blue shades for informational content

## 🚀 Next Steps

1. Test all components in different states
2. Verify accessibility and contrast ratios
3. Update any remaining hardcoded colors
4. Consider dark mode implementation
5. Update brand assets and documentation

## 📱 Mobile Considerations

The color theme is fully responsive and works across all device sizes. Special attention has been paid to:
- Touch target contrast
- Readability on small screens
- Consistent theming in mobile layouts
