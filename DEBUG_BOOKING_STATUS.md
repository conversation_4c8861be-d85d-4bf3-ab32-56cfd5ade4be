# Debug Booking Status Issue

## Current Issue
- Booking for Sep 4th, 10:00-12:00 is appearing in Past tab instead of Upcoming

## Analysis

### Current Logic (FIXED):
```javascript
const getBookingStatus = (booking) => {
  const now = new Date();
  
  // Create booking date with time
  const bookingDate = new Date(booking.date);
  const [hours, minutes] = booking.timeSlot.endTime.split(':').map(Number);
  const bookingEndDateTime = new Date(bookingDate);
  bookingEndDateTime.setHours(hours, minutes, 0, 0);

  if (booking.status === 'cancelled') return 'cancelled';
  
  // Check if booking has ended (past the end time)
  if (bookingEndDateTime < now) return 'completed';
  
  // Check if booking is today
  if (isToday(bookingDate)) return 'today';
  
  return 'upcoming';
};
```

### What was wrong before:
- Only compared dates, not times
- A booking for today would show as "completed" even if it hadn't started yet

### What's fixed now:
- Compares the actual end time of the booking
- Only marks as "completed" if the booking has actually ended
- <PERSON><PERSON><PERSON> handles same-day bookings

## Test Cases:

### Booking: Sep 4th, 10:00-12:00
- If current time is Sep 4th, 9:00 AM → Status: "today"
- If current time is Sep 4th, 11:00 AM → Status: "today" 
- If current time is Sep 4th, 1:00 PM → Status: "completed"
- If current time is Sep 3rd, any time → Status: "upcoming"
- If current time is Sep 5th, any time → Status: "completed"

This should now work correctly!
